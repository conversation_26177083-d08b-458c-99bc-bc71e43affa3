// Copyright (c) Redback Technologies. All Rights Reserved.

package com.ebon.energy.fms.common.enums;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 固件版本枚举类
 */
@Getter
@RequiredArgsConstructor
public enum FirmwareVersion {

    V06("06", false, false),
    V06T("06T", false, false),
    V07A("07A", false, false),
    V07B("07B", false, false),
    V07C("07C", false, false),
    V11110("11110", false, false),
    V11111("11111", false, false),
    V11112("11112", false, false),
    V12121("12121", true, false),
    V12123("12123", true, true),
    V13134("13134", true, true),
    V13135("13135", true, true),
    V13136("13136", true, true),
    V13137("13137", true, true),
    V13138("13138", true, true),
    V13139("13139", true, true),
    V14141("14141", true, true),
    V14142("14142", true, true),
    V14143("14143", true, true),
    V14144("14144", true, true),
    V14145("14145", true, true),
    V14146("14146", true, true),
    V14147("14147", true, true),
    V14148("14148", true, true),
    V14149("14149", true, true),
    V151507("151507", true, true),
    // ROSS 1 reports firmware with no zero prefix on ARM
    // (https://rbtech.visualstudio.com/DefaultCollection/Redback/_workitems/edit/36102)
    V15157("15157", true, true),
    V161608("161608", true, true),
    // ROSS 1 reports firmware with no zero prefix on ARM
    // (https://rbtech.visualstudio.com/DefaultCollection/Redback/_workitems/edit/36102)
    V16168("16168", true, true),
    V171709("171709", true, true),
    // ROSS 1 reports firmware with no zero prefix on ARM
    // (https://rbtech.visualstudio.com/DefaultCollection/Redback/_workitems/edit/36102)
    V17179("17179", true, true),
    V181810("181810", true, true),
    V181812("181812", true, true),
    V202012("202012", true, true),
    V212112("212112", true, true),
    V222214("222214", true, true),
    V232314("232314", true, true),

    // Three Phase system firmware
    V030311("030311", false, true),
    V050514("050514", false, true),
    V050516("050516", false, true),
    V060616("060616", false, true),
    V080819("080819", false, true),

    // Smart Battery system firmware
    V030314("030314", false, true),
    V030316("030316", false, true),
    V040418("040418", false, true),
    V040419("040419", false, true),

    INVALID("Invalid", false, false),
    UNKNOWN(null, false, false);

    @JsonProperty("VersionString")
    private final String versionString;

    @JsonProperty("SupportsACCoupled")
    private final boolean supportsACCoupled;

    @JsonProperty("SupportsWACountry")
    private final boolean supportsWACountry;

    private static final Map<String, FirmwareVersion> VERSION_LOOKUP = new HashMap<>();
    private static final Pattern VERSION_PATTERN = Pattern.compile(".{2}(?!$)");

    static {
        for (FirmwareVersion version : values()) {
            if (version.versionString != null) {
                VERSION_LOOKUP.put(version.versionString, version);
            }
        }
    }

    /**
     * 获取版本对象
     */
    public Optional<com.ebon.energy.fms.common.utils.Version> getVersion() {
        try {
            if (versionString == null) {
                return Optional.empty();
            }
            // add the points into the firmware version
            String firmware = VERSION_PATTERN.matcher(versionString).replaceAll("$0.");
            return Optional.of(new com.ebon.energy.fms.common.utils.Version(firmware));
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    /**
     * 是否支持Western Power Horizon
     */
    public boolean getSupportsWesternPowerHorizon() {
        if (versionString == null) {
            return false;
        }

        try {
            return getVersion()
                    .map(version -> version.getMajor() >= 17)
                    .orElse(false);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从版本字符串获取固件版本
     */
    public static FirmwareVersion fromVersionString(String versionString) {
        if (versionString != null && VERSION_LOOKUP.containsKey(versionString)) {
            return VERSION_LOOKUP.get(versionString);
        }
        return UNKNOWN;
    }

    /**
     * 是否支持Unity或默认
     */
    public boolean supportsUnityOrDefault() {
        return getVersion()
                .map(version -> {
                    com.ebon.energy.fms.common.utils.Version noSupportVersion = new com.ebon.energy.fms.common.utils.Version(
                            "15.15.7");
                    return version.compareTo(noSupportVersion) > 0;
                })
                .orElse(false);
    }

    /**
     * 是否支持高导出
     */
    public boolean supportsHighExport() {
        return getVersion()
                .map(version -> {
                    com.ebon.energy.fms.common.utils.Version noSupportVersion = new com.ebon.energy.fms.common.utils.Version(
                            "12.12.3");
                    return version.compareTo(noSupportVersion) >= 0;
                })
                .orElse(false);
    }

    @Override
    public String toString() {
        return versionString != null ? versionString : "Unknown";
    }
}