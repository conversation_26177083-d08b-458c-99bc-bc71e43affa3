package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebon.energy.fms.domain.entity.SiteCoordinatorSiteDataDO;
import com.ebon.energy.fms.domain.entity.SiteDO;
import com.ebon.energy.fms.domain.entity.SiteDeviceDO;
import com.ebon.energy.fms.domain.po.SiteListPO;
import com.ebon.energy.fms.domain.vo.SiteVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SiteMapper extends BaseMapper<SiteDO> {

    @Select({
            "<script>",
            "SELECT s.SystemId AS SiteId, s.AddressId, siteStatus.onlineStatus, siteStatus.deviceCount",
            "FROM site s ",
            "LEFT JOIN (",
            "  SELECT ",
            "    si.SiteId,",
            "    COUNT(RedbackProductSn) AS deviceCount, ",
            "    CASE ",
            "      WHEN COUNT(si.RedbackProductSn) = 0 THEN 0 ",
            "      WHEN SUM(si.IsOnline) = COUNT(si.RedbackProductSn) THEN 3 ",
            "      WHEN SUM(si.IsOnline) > 0 THEN 2 ",
            "      ELSE 1 ",
            "    END AS onlineStatus",
            "  FROM (",
            "    SELECT ",
            "      s.SystemId AS SiteId, r.RedbackProductSn, ",
            "      CASE WHEN m.MinutesSinceLastTelemetry &lt;= 10 THEN 1 ELSE 0 END AS IsOnline ",
            "    FROM site s ",
            "    LEFT JOIN RedbackProductInstallations r WITH(NOLOCK) ON s.Id = r.SiteId and r.InstallationEndDate is null",
            "    LEFT JOIN MetricsTelemetry m WITH(NOLOCK) ON r.RedbackProductSn = m.SerialNumber ",
            "  ) si ",
            "  GROUP BY si.SiteId",
            ") siteStatus ON s.SystemId = siteStatus.SiteId ",
            "<where>",
            "  <if test=\"query.siteId != null\">",
            "    AND s.SystemId = #{query.siteId}",
            "  </if>",
            "  <if test=\"query.onlineStatus != null\">",
            "    AND siteStatus.onlineStatus = #{query.onlineStatus}",
            "  </if>",
            "</where>",
            " ORDER BY siteStatus.onlineStatus DESC, siteStatus.deviceCount DESC",
            "</script>"
    })
    Page<SiteVO> selectSitePage(Page<SiteVO> page, @Param("query") SiteListPO query);
    
    @Select(/*"WITH UserDevices AS ( " +
            "    SELECT DISTINCT asn.SerialNumber " +
            "    FROM dbo.AllowedSerialNumbers(@ExecutingUserId) asn " +
            ") " +*/
            "SELECT  " +
            "    installation.RedbackProductSn as SerialNumber,  " +
            "    product.LatestSystemStatus, " +
            "    product.LastSystemStatusReceived as lastSystemStatusReceivedUtc, " +
            "    IsInWarranty, " +
            "    addr.TimeZoneId as BclTimeZoneId, " +
            "    maintainingInstaller.MaintainingInstallerId as MaintainingInstallerId, " +
            "    site.SupportsLoadContributors, " +
            "    site.HasReports, " +
            "    site.EnergyBalancingMethod, " +
            "    installation.Phase, " +
            "    installation.PhaseRole, " +
            "    installation.InstallationStartDate as InstallationDateUtc " +
            "FROM Site site " +
            "inner join RedbackProductInstallations installation on installation.SiteId = site.Id and installation.InstallationEndDate is null " +
            //"-- security " +
            //"inner join UserDevices asn on installation.RedbackProductSn = asn.SerialNumber " +
            "inner join RedbackProducts product on product.RedbackProductSn = installation.RedbackProductSn " +
            "inner join Addresses addr on addr.Id = site.AddressId " +
            //"-- Maintaining Installer " +
            "LEFT JOIN ( SELECT usr.Id AS MaintainingInstallerId, access.RedbackProductsn  " +
            "      FROM RedbackProductAccesses access " +
            "      INNER JOIN Configurations configuration on configuration.Id = access.ConfigurationId and configuration.ConfigurationType = 2 " +
            "      INNER JOIN RedbackUsers usr on usr.Id = access.RedbackUserId and usr.UserType = 2 ) maintainingInstaller " +
            "ON maintainingInstaller.RedbackProductsn = installation.RedbackProductsn " +
            "WHERE site.SystemId = #{systemId, jdbcType=VARCHAR} "
            /*"-- Ignore site if the user doesn't have access to one or more products within it -- " +
            "AND NOT EXISTS " +
            "( " +
            "    SELECT 1 " +
            "    FROM dbo.RedbackProductInstallations ins2 " +
            "    LEFT OUTER JOIN UserDevices asn2 ON asn2.SerialNumber = ins2.RedbackProductSn " +
            "    WHERE ins2.SiteId = site.Id " +
            "    AND asn2.SerialNumber IS NULL " +
            ")"*/)
    List<SiteDeviceDO> selectSiteDevices(@Param("systemId") String systemId);

    @Select("SELECT site.SystemId FROM Site site " +
            "inner join RedbackProductInstallations installation on installation.SiteId = site.Id and installation.InstallationEndDate is null " +
            "WHERE installation.RedbackProductSn = #{serialNumber, jdbcType=VARCHAR}")
    String selectSiteId(@Param("serialNumber") String serialNumber);

    @Select("<script>" +
                    "SELECT  " +
                    "    site.SystemId as siteId, " +
                    "    installation.RedbackProductSn as SerialNumber,  " +
                    "    product.LatestSystemStatus, " +
                    "    product.LastSystemStatusReceived as lastSystemStatusReceivedUtc, " +
                    "    IsInWarranty, " +
                    "    addr.TimeZoneId as BclTimeZoneId, " +
                    "    maintainingInstaller.MaintainingInstallerId as MaintainingInstallerId, " +
                    "    site.SupportsLoadContributors, " +
                    "    site.HasReports, " +
                    "    site.EnergyBalancingMethod, " +
                    "    installation.Phase, " +
                    "    installation.PhaseRole, " +
                    "    installation.InstallationStartDate as InstallationDateUtc " +
                    "FROM Site site " +
                    "inner join RedbackProductInstallations installation on installation.SiteId = site.Id and installation.InstallationEndDate is null " +
                    "inner join RedbackProducts product on product.RedbackProductSn = installation.RedbackProductSn " +
                    "inner join Addresses addr on addr.Id = site.AddressId " +
                    //"-- Maintaining Installer " +
                    "LEFT JOIN ( SELECT usr.Id AS MaintainingInstallerId, access.RedbackProductsn  " +
                    "      FROM RedbackProductAccesses access " +
                    "      INNER JOIN Configurations configuration on configuration.Id = access.ConfigurationId and configuration.ConfigurationType = 2 " +
                    "      INNER JOIN RedbackUsers usr on usr.Id = access.RedbackUserId and usr.UserType = 2 ) maintainingInstaller " +
                    "ON maintainingInstaller.RedbackProductsn = installation.RedbackProductsn " +
                    "WHERE site.SystemId in " +
                    "<foreach item='item' collection='systemIds' open='(' separator=',' close=')'>" +
                    "#{item}" +
                    "</foreach>" +
                    "</script>")
    List<SiteDeviceDO> selectSiteDevicesBatch(@Param("systemIds") List<String> systemIds);

    @Select(" declare @siteId uniqueidentifier " +
            " set @siteId = (select siteId from dbo.RedbackProductInstallations where InstallationEndDate is null and RedbackProductSn = #{serialNumber}) " +
            "SELECT  " +
            "     p.RedbackProductSn serialNumber,  " +
            "  sysStatus.RossVersion version, " +
            "  cast(CTComms as bit) isConnected,  " +
            "  sysStatus.ModelName as model, " +
            "     '20' + SUBSTRING(p.RedbackProductSn,3, 6) manufactureDate,  " +
            "     SUBSTRING(p.RedbackProductSn, 13, 4) manufactureSerialNumber,  " +
            "     i.PhaseRole siteRole, " +
            "  hm.IsGridTie isGridTie, " +
            "     s.SystemId siteId " +
            " from dbo.RedbackProducts p     " +
            "  CROSS APPLY OPENJSON(p.LatestSystemStatus) WITH ( " +
            "   RossVersion VARCHAR(MAX) '$.OuijaBoard.SoftwareVersion', " +
            "   FirmwareVersion VARCHAR(MAX) '$.Inverter.FirmwareVersion', " +
            "   CTComms VARCHAR(MAX) '$.OuijaBoard.CTComms', " +
            "   ModelName VARCHAR(MAX) '$.Inverter.ModelName' " +
            "   ) sysStatus " +
            " join dbo.RedbackProductInstallations i on i.RedbackProductSn = p.RedbackProductSn and i.InstallationEndDate is null " +
            " join dbo.Site s on s.Id = i.SiteId " +
            " LEFT JOIN dbo.HardwareModel hm ON hm.Name = sysStatus.ModelName " +
            " where s.Id = @siteId")
    List<SiteCoordinatorSiteDataDO> selectSiteDataBySerialNumber(@Param("serialNumber") String serialNumber);

    @Select("SELECT addr.TimeZoneId " +
            "FROM Site site " +
            "INNER JOIN Addresses addr ON addr.Id = site.AddressId " +
            "WHERE site.SystemId = #{publicSiteId, jdbcType=VARCHAR}")
    String getBclTimeZoneIdForSite(@Param("publicSiteId") String publicSiteId);


}