package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.GroupEventDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 组事件Mapper接口
 */
@Mapper
public interface GroupEventMapper extends BaseMapper<GroupEventDO> {

    /**
     * 根据ID查询组事件，包含受影响的产品列表
     * 
     * @param id 组事件ID
     * @return 组事件信息
     */
    @Select("SELECT * " +
            "FROM GroupEvents ge " +
            "WHERE ge.Id = #{id}")
    GroupEventDO selectByIdWithAffectedProducts(@Param("id") Integer id);
}
