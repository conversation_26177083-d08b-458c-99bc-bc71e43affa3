package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.*;
import com.ebon.energy.fms.domain.vo.ElectricalConfigurationDto;
import com.ebon.energy.fms.domain.vo.ProductElectricalConfigurationVO;
import com.ebon.energy.fms.domain.vo.product.control.ProductInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ProductMapper extends BaseMapper<RedbackProductsDO> {

        @Select("SELECT " +
                        "JSON_VALUE(product.LatestSystemStatus, '$.Inverter.ModelName') as ModelName, " +
                        "JSON_VALUE(product.LatestSystemStatus, '$.Inverter.FirmwareVersion') as FirmwareVersion, " +
                        "settings.DesiredDeviceSettings as Desired, " +
                        "settings.ReportedDeviceSettings as Reported " +
                        "FROM RedbackProducts product " +
                        // "--LEFT JOINS because ROSS1 AND ROSS2 " +
                        "LEFT JOIN Device device on device.SerialNumber = product.RedbackProductSn AND device.ApplicationName = 'Ross' "
                        +
                        "LEFT JOIN DeviceSettings settings on settings.DeviceId = device.Id " +
                        "WHERE RedbackProductSn = #{serialNumber, jdbcType=VARCHAR}")
        ModelInfoDO selectModelInfo(@Param("serialNumber") String serialNumber);

        @Select("SELECT" +
                        " p.RedbackProductSn AS serialNumber, i.InstallationStartDate AS activeInstallationDate, " +
                        "p.IsInWarranty AS isInWarranty, i.WarrantyEndDate As warrantyEndDate, " +
                        "REPLACE(JSON_VALUE(p.LatestSystemStatus, 'lax $.OuijaBoard.SoftwareVersion'), 'ROSS v', '') AS rossVersion, "
                        +
                        "p.HardwareConfig AS hardwareConfig, JSON_VALUE(p.LatestSystemStatus, '$.Inverter.ModelName') AS modelName, "
                        +
                        "JSON_VALUE(p.LatestSystemStatus, 'lax $.Date') AS lastConnected, p.LatestSystemStatus AS latestSystemStatus, "
                        +
                        "p.LastSystemStatusReceived AS lastSystemStatusReceived " +
                        "FROM dbo.RedbackProducts p " +
                        "LEFT JOIN dbo.RedbackProductInstallations i ON i.RedbackProductSn = p.RedbackProductSn AND i.InstallationEndDate IS NULL "
                        +
                        "WHERE p.RedbackProductSn = #{serialNumber, jdbcType=VARCHAR}")
        ProductWithInstallationDO selectWithInstallation(@Param("serialNumber") String serialNumber);

        @Select("SELECT  " +
                        "    p.RedbackProductSn SerialNumber " +
                        "    , i.InstallationStartDate DateCreated " +
                        "    , p.LastSystemStatusReceived " +
                        "    , p.ProductOwnerId OwnerId " +
                        "    , i.InstallationInstallerCompany OriginalInstallerCompanyId  " +
                        "    , ucf.ClaimValue OwnerFirstName " +
                        "    , ucl.ClaimValue OwnerLastName " +
                        "    , cc.ClaimValue CompanyName " +
                        "    , s.SystemId PublicSiteId " +
                        "    , s.Nmi " +
                        "    , s.Id SiteId " +
                        "    , ucm.ClaimValue OwnerContact " +
                        "    , u.email OwnerEmail " +
                        "    , a.AddressLineOne " +
                        "    , a.AddressLineTwo " +
                        "    , a.Suburb " +
                        "    , a.State " +
                        "    , a.PostCode " +
                        "    , a.Country " +
                        "FROM dbo.RedbackProducts p " +
                        "join dbo.RedbackProductInstallations i on i.RedbackProductSn = p.RedbackProductSn " +
                        "join dbo.Site s on i.SiteId = s.Id " +
                        "left join dbo.AspNetUserClaims_External ucf on ucf.userId = p.ProductOwnerId and ucf.ClaimType = 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name' "
                        +
                        "left join dbo.AspNetUserClaims_External ucl on ucl.userId = p.ProductOwnerId and ucl.ClaimType = 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname' "
                        +
                        "left join dbo.AspNetUserClaims_External ucm on ucm.userId = p.ProductOwnerId and ucm.ClaimType = 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/mobilephone' "
                        +
                        "left join dbo.AspNetUserClaims_External cc on cc.userId = i.InstallationInstallerCompany and cc.ClaimType = 'CompanyName' "
                        +
                        "left join dbo.AspNetUsers_External u on u.Id = p.ProductOwnerId " +
                        "left join dbo.Addresses a on i.AddressId = a.id " +
                        "where i.InstallationEndDate is null " +
                        "and p.RedbackProductSn = #{serialNumber, jdbcType=VARCHAR}")
        ProductWithOwnerDO selectWithOwner(@Param("serialNumber") String serialNumber);

        @Select("<script>" +
                        "SELECT  " +
                        "    p.RedbackProductSn SerialNumber " +
                        "    , i.InstallationStartDate DateCreated " +
                        "    , p.LastSystemStatusReceived " +
                        "    , p.ProductOwnerId OwnerId " +
                        "    , i.InstallationInstallerCompany OriginalInstallerCompanyId  " +
                        "    , ucf.ClaimValue OwnerFirstName " +
                        "    , ucl.ClaimValue OwnerLastName " +
                        "    , cc.ClaimValue CompanyName " +
                        "    , s.SystemId PublicSiteId " +
                        "    , s.Nmi " +
                        "    , s.Id SiteId " +
                        "    , ucm.ClaimValue OwnerContact " +
                        "    , u.email OwnerEmail " +
                        "    , a.AddressLineOne " +
                        "    , a.AddressLineTwo " +
                        "    , a.Suburb " +
                        "    , a.State " +
                        "    , a.PostCode " +
                        "    , a.Country " +
                        "FROM dbo.RedbackProducts p " +
                        "join dbo.RedbackProductInstallations i on i.RedbackProductSn = p.RedbackProductSn " +
                        "join dbo.Site s on i.SiteId = s.Id " +
                        "left join dbo.AspNetUserClaims_External ucf on ucf.userId = p.ProductOwnerId and ucf.ClaimType = 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name' "
                        +
                        "left join dbo.AspNetUserClaims_External ucl on ucl.userId = p.ProductOwnerId and ucl.ClaimType = 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname' "
                        +
                        "left join dbo.AspNetUserClaims_External ucm on ucm.userId = p.ProductOwnerId and ucm.ClaimType = 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/mobilephone' "
                        +
                        "left join dbo.AspNetUserClaims_External cc on cc.userId = i.InstallationInstallerCompany and cc.ClaimType = 'CompanyName' "
                        +
                        "left join dbo.AspNetUsers_External u on u.Id = p.ProductOwnerId " +
                        "left join dbo.Addresses a on i.AddressId = a.id " +
                        "where i.InstallationEndDate is null " +
                        "and p.RedbackProductSn in " +
                        "<foreach item='item' collection='serialNumbers' open='(' separator=',' close=')'>" +
                        "#{item}" +
                        "</foreach>" +
                        "</script>")
        List<ProductWithOwnerDO> selectWithOwnerBatch(@Param("serialNumbers") List<String> serialNumbers);

        /**
         * Get product information by serial number
         *
         * @param serialNumber Product serial number
         * @return Product information
         */
        @Select("SELECT " +
                        "RedbackProductSn as serialNumber, " +
                        "(SELECT top 1 Configurations FROM Configurations c WHERE c.RedbackProductSn = p.RedbackProductSn AND ConfigurationType = 2) as cloudConfiguration, "
                        +
                        "(SELECT top 1 ConfigurationsOnDevice FROM Configurations c WHERE c.RedbackProductSn = p.RedbackProductSn AND ConfigurationType = 2) as deviceConfiguration, "
                        +
                        "LatestSystemStatus as latestSystemStatus, " +
                        "AppliedTariffId as appliedTariffId, " +
                        "IsOptInForOptimization as optInForOptimization, " +
                        "(SELECT top 1 Value FROM RedbackProductDetails rpd WHERE rpd.RedbackProductSn = p.RedbackProductSn AND Name = 'IsAllowedToOptimise') as canBeOptimised, "
                        +
                        "ProductOwnerId as ownerId, " +
                        "JSON_VALUE(LatestSystemStatus, '$.OuijaBoard.TimeZone') as inverterTimeZone " +
                        "FROM RedbackProducts p " +
                        "WHERE RedbackProductSn = #{serialNumber}")
        ProductInfo getProductInfo(@Param("serialNumber") String serialNumber);

        @Select("select top 1 TimeZoneId " +
                        "from RedbackProducts " +
                        "         left join RedbackProductInstallations " +
                        "                   on RedbackProductInstallations.RedbackProductSn = RedbackProducts.RedbackProductSn "
                        +
                        "         left join Addresses on RedbackProductInstallations.AddressId = Addresses.ID " +
                        "where RedbackProducts.RedbackProductSn = #{serialNumber} and RedbackProductInstallations.InstallationEndDate is null")
        String queryProductTimeZone(@Param("serialNumber") String serialNumber);

        @Select("declare @siteId uniqueidentifier; " +
                        "set @siteId = (select id from dbo.Site s where s.SystemId = #{SystemId}) " +
                        "SELECT p.RedbackProductSn SerialNumber " +
                        "    , JSON_VALUE(LatestSystemStatus, '$.Inverter.ModelName') Model " +
                        "    , JSON_VALUE(ds.ReportedDeviceSettings, '$.site.inverterRole') CurrentRole " +
                        "    , '' Status " +
                        "    , JSON_VALUE(LatestSystemStatus, '$.OuijaBoard.SoftwareVersion') RossVersion " +
                        "    , JSON_VALUE(LatestSystemStatus, '$.Inverter.FirmwareVersion') FirmwareVersion " +
                        "    , JSON_VALUE(LatestSystemStatus, '$.OuijaBoard.CTComms') Meter " +
                        "    , JSON_VALUE(LatestSystemStatus, '$.OuijaBoard.ComputerName') ComputerName " +
                        "    , cast(JSON_VALUE(LatestSystemStatus, '$.Date') as datetime2) AS LatestTelemetryUtc " +
                        "    , i.PhaseRole SelectedRole " +
                        "    , OffComms = cast(CASE " +
                        "        WHEN details.value = 'True' THEN 1  " +
                        "        ELSE 0 " +
                        "      END as bit) " +
                        "    , p.IsInWarranty " +
                        "    , cast(case when i.AddressId is not null then 1 else 0 end as bit) IsRegistered " +
                        "    , cast(case when p.LastSystemStatusReceived is null and details.Value is null then 1 else 0 end as bit) IsPending "
                        +
                        "    , cast(case when p.LastSystemStatusReceived is not null and datediff(n, p.LastSystemStatusReceived, GETUTCDATE()) <= 10 then 1 else 0 end as bit) IsOnline "
                        +
                        "    , #{SystemId} PublicSiteId " +
                        "FROM dbo.RedbackProducts p " +
                        "join dbo.RedbackProductInstallations i on p.RedbackProductSn = i.RedbackProductSn and i.InstallationEndDate is null "
                        +
                        "LEFT JOIN dbo.RedbackProductInstallationDetails details ON details.RedbackProductInstallationId = i.Id AND details.Name = 'IsSupposedToHaveInternetConnection' "
                        +
                        "left join dbo.Device d on d.SerialNumber = p.RedbackProductSn and d.ApplicationName = 'Ross' "
                        +
                        "left join dbo.DeviceSettings ds on ds.DeviceId = d.id " +
                        "where ISJSON(p.LatestSystemStatus) = 1 " +
                        "and i.SiteId = @siteId")
        List<SiteInverterInfoDO> selectSiteInverters(@Param("SystemId") String SystemId);

        @Select("<script>" +
                        "SELECT distinct p.RedbackProductSn SerialNumber " +
                        "    , i.PhaseRole Role " +
                        "    , i.SiteId " +
                        "    , case when ISJSON(p.LatestSystemStatus) = 1 then JSON_VALUE(LatestSystemStatus, '$.OuijaBoard.SoftwareVersion') else '' end Version "
                        +
                        "    , d.DeviceId " +
                        "    , s.SystemId PublicSiteId " +
                        "FROM dbo.RedbackProducts p " +
                        "left join dbo.Device d on d.SerialNumber = p.RedbackProductSn AND d.ApplicationName = 'Ross' "
                        +
                        "join dbo.RedbackProductInstallations i on p.RedbackProductSn = i.RedbackProductSn and i.InstallationEndDate is null "
                        +
                        "join dbo.Site s on s.Id = i.SiteId " +
                        "where i.SiteId in ( " +
                        "    select ii.SiteId " +
                        "    from dbo.RedbackProductInstallations ii " +
                        "    where ii.InstallationEndDate is null and ii.RedbackProductSn in " +
                        "<foreach item='item' collection='serialNumbers' open='(' separator=',' close=')'>" +
                        "#{item}" +
                        "</foreach>" +
                        ")" +
                        "</script>")
        List<InverterRoleSiteIdDO> selectSiteInfoBySerialNumbers(@Param("serialNumbers") List<String> serialNumbers);

        /**
         * 获取产品的电气配置信息
         * 对应 EF 查询：
         * var product = Db.RedbackProducts
         * .Include(p => p.Configurations)
         * .Where(p => p.RedbackProductSn == serialNumber)
         * .Select(p => new
         * {
         * ElectricalConfiguration = p.Configurations
         * .Where(c => c.ConfigurationType == ConfigurationType.Electrical)
         * .Select(c => new
         * {
         * Cloud = c.Configurations,
         * Device = c.ConfigurationsOnDevice
         * })
         * .FirstOrDefault()
         * })
         * .FirstOrDefault();
         *
         * @param serialNumber 产品序列号
         * @return 产品电气配置信息
         */
        @Select("SELECT TOP 1 " +
                        "c.Configurations as cloud, " +
                        "c.ConfigurationsOnDevice as device " +
                        "FROM dbo.RedbackProducts p " +
                        "LEFT JOIN dbo.Configurations c ON c.RedbackProductSn = p.RedbackProductSn " +
                        "WHERE p.RedbackProductSn = #{serialNumber, jdbcType=VARCHAR} " +
                        "AND c.ConfigurationType = 0")
        ElectricalConfigurationDto selectElectricalConfiguration(@Param("serialNumber") String serialNumber);

        /**
         * 查询产品及其电气配置信息
         * 对应C#代码中的复杂查询，用于电气配置更新
         *
         * @param serialNumber 产品序列号
         * @return 产品配置信息
         */
        @Select("SELECT " +
                        "p.ProductOwnerId, " +
                        "c.Id as configurationId, " +
                        "c.Configurations, " +
                        "c.ConfigurationsOnDevice, " +
                        "c.ModifiedDateTime, " +
                        "c.LastModifiedById, " +
                        "c.RowVersion " +
                        "FROM dbo.RedbackProducts p " +
                        "LEFT JOIN dbo.Configurations c ON c.RedbackProductSn = p.RedbackProductSn " +
                        "AND c.ConfigurationType = #{configurationType, jdbcType=INTEGER} " +
                        "WHERE p.RedbackProductSn = #{serialNumber, jdbcType=VARCHAR}")
        ProductElectricalConfigurationVO selectProductWithElectricalConfiguration(
                @Param("serialNumber") String serialNumber,
                @Param("configurationType") Integer configurationType);

}
