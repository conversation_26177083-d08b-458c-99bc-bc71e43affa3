package com.ebon.energy.fms.mapper.third;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ebon.energy.fms.domain.entity.ProductGroupComplianceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 产品组合规性Mapper接口
 */
@Mapper
public interface ProductGroupComplianceMapper extends BaseMapper<ProductGroupComplianceDO> {

    /**
     * 根据组事件ID查询受影响的产品列表
     * 
     * @param groupEventId 组事件ID
     * @return 受影响的产品列表
     */
    @Select("SELECT * " +
            "FROM ProductGroupCompliance " +
            "WHERE GroupEventId = #{groupEventId}")
    List<ProductGroupComplianceDO> selectByGroupEventId(@Param("groupEventId") Integer groupEventId);
}
