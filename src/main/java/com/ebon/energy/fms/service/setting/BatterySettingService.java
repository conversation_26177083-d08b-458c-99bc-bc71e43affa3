package com.ebon.energy.fms.service.setting;

import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.controller.request.ApllyBatterySettingRequest;
import com.ebon.energy.fms.domain.vo.ElectricalConfiguration;
import com.ebon.energy.fms.domain.vo.ElectricalViewModel;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.domain.vo.setting.UniversalSettingValueDto;
import com.ebon.energy.fms.domain.vo.setting.UpdateElectricalConfigurationDTO;
import com.ebon.energy.fms.repository.RedbackRepository;
import com.ebon.energy.fms.repository.impl.SpecificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class BatterySettingService {

    private final RedbackRepository productDbRepository;

    private final SpecificationRepository specificationRepository;

    public Map<UniversalSettingId, UniversalSettingValueDto> getRoss1SettingsBattery(String sn) {
        var data = productDbRepository.getElectricalControllerDataRoss1(sn);
        var model = new ElectricalViewModel();
        model.setIsUserReadOnly(data.getIsReadOnly());
        model.setIsGridOn(data.getIsGridOn());
        model.setSerial(sn);
        model.setProductDefaults(specificationRepository.getInstallationSpecAsync(sn));
        model.setIsInSync(data.getIsInSync());
        model.setDeviceTimestamp(data.getDeviceTimestamp());
        model.setCloudTimestamp(data.getCloudTimestamp());
        model.setIsACCoupledSupported(data.getIsACCoupledSupported());
        model.setIsWASafetyCountrySupported(data.getIsWASafetyCountrySupported());
        model.populateFromElectricalConfiguration(data.getElectricalConfiguration());
        return null;
    }

    public void applyRoss1SettingsBattery(String sn, ApllyBatterySettingRequest request) {
        var model = request.getModel();
        var data = productDbRepository.getElectricalControllerDataRoss1(sn);
        request.getModel().setProductDefaults(specificationRepository.getInstallationSpecAsync(sn));
        request.validateElectricalConfiguration(request.getModel());
        var config = data.getElectricalConfiguration();
        model.applyChangesToConfiguration(config);
        adjustLimitExportPower(config, data.getIsACCoupledSupported(), model.getProductDefaults());
        productDbRepository.updateElectricalSettingRoss1(UpdateElectricalConfigurationDTO.builder().sn(sn).electricalConfiguration(config).build());
    }

    private void adjustLimitExportPower(
            ElectricalConfiguration config,
            boolean isACCoupledSupported,
            InstallationSpecification productModelDefaults) {

        int maxExportPowerW = productModelDefaults.getMaximumExportPowerInWatts();
        boolean shouldLimitExportPower = Optional.ofNullable(config.getIsLimitExportPower()).orElse(false);

        // If the user has selected to LEP, and the power is the maximum, we subtract 1.
        // This is because in some old version of ROSS, a full value meant that LEP was not to be used.
        // If AC Coupled is supported then we know for sure that the ROSS is new enough to work correctly.
        if (shouldLimitExportPower &&
                Objects.equals(config.getLimitExportPower(), maxExportPowerW) &&
                !isACCoupledSupported) {
            config.setLimitExportPower(maxExportPowerW - 1);
        }
        // Else, if the toggle is false then we set the LEP to the maximum value so that old ROSS
        // versions will disable LEP.
        else if (!shouldLimitExportPower) {
            config.setLimitExportPower(maxExportPowerW);
        }
        // Else, we're limiting the export power, but we know AC Coupled is supported or it's not the maximum power and no adjusting is required
        else {
            // do nothing
        }
    }

}
