package com.ebon.energy.fms.service.factory;

import com.ebon.energy.fms.common.enums.EnergyFlowIssue;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.BatteryStatusValue;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.BatteryStatus;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.Tuple3;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.ebon.energy.fms.domain.vo.EnergyFlowCoherentInputVO.PowerConsideredIdleW;
import static com.ebon.energy.fms.util.SafeAccess.getValue;

public class EnergyFlowFactory {

    public static Tuple2<EnergyFlowInputVO, EnergyFlowVO> Build(EnergyFlowInputVO input) {
        if (input == null) {
            throw new IllegalArgumentException("input cannot be null");
        }

        List<EnergyFlowIssue> issues = new ArrayList<>();

        Tuple2<Watt, EnergyFlowIssue> gridResult = getGrid(input.getGridNegativeIsImportW(), input.getGridStatus());
        if (gridResult._2 != null) {
            issues.add(gridResult._2);
        }

        Tuple3<Watt, List<EnergyFlowIssue>, BigDecimal> batteryResult = getBattery(
                input.getConfiguredWithBatteries(),
                input.getBatteryNegativeIsChargingW(),
                input.getBatteryStatus(),
                input.getBatterySoC0to100()
        );
        if (batteryResult._2 != null && !batteryResult._2.isEmpty()) {
            issues.addAll(batteryResult._2);
        }

        Watt pVW;
        if (input.getSupportsConnectedPV()) {
            pVW = Watt.getValueOrZero(input.getPVW());
        } else {
            pVW = null;
            if (input.getPVW() != null && input.getPVW().getValue() != null && input.getPVW().getValue().compareTo(BigDecimal.ZERO) != 0) {
                issues.add(EnergyFlowIssue.SupportsConnectedPVFalseButPVP);
            }
        }

        EnergyFlowCoherentInputVO inputRnd = new EnergyFlowCoherentInputVO(
                Watt.getValueOrZero(input.getACLoadW()),
                Watt.getValueOrZero(input.getBackupLoadW()),
                pVW,
                input.getThirdPartyW(),
                gridResult._1,
                batteryResult._1,
                input.getConfiguredWithBatteries(),
                batteryResult._3,
                input.getCtComms() != null ? input.getCtComms() : false,
                issues
        );

        return Build(input, inputRnd);
    }

    public static EnergyFlowExtendedVO buildForViewModels(DataForDashboardVO dataForDashboard,
                                                          Boolean configuredWithBatteries,
                                                          Boolean supportsConnectedPV) {
        if (dataForDashboard == null) {
            return null;
        }

        Tuple2<EnergyFlowInputVO, EnergyFlowVO> r = Build(
                dataForDashboard,
                configuredWithBatteries,
                supportsConnectedPV
        );

        return new EnergyFlowExtendedVO(r._1, new EnergyFlowExtVO(
                r._2.getACLoadW(),
                r._2.getBackupLoadW(),
                r._2.getPVW(),
                r._2.getThirdPartyW(),
                r._2.getGridNegativeIsImportW(),
                r._2.getHasBatteries(),
                r._2.getBatteryNegativeIsChargingW(),
                r._2.getBatterySoC0to1(),
                r._2.getIssues(),
                dataForDashboard.getLastStatusDateUtc(),
                dataForDashboard.getWindowsTime() != null ? dataForDashboard.getWindowsTime() : null,
                dataForDashboard.getBatterySummary() != null && dataForDashboard.getBatterySummary().getStatus() == BatteryStatusValue.Disabled,
                dataForDashboard.getDRM0Enable() != null ? dataForDashboard.getDRM0Enable() : false
        ));
    }

    public static Tuple2<EnergyFlowInputVO, EnergyFlowVO> Build(DataForDashboardVO dataForDashboard,
                                                                   Boolean configuredWithBatteries,
                                                                   boolean supportsConnectedPV) {
        if (dataForDashboard == null) {
            throw new IllegalArgumentException("dataForDashboard cannot be null");
        }

        BatteryStatus batterySummary = dataForDashboard.getBatterySummary();

        EnergyFlowInputVO raw = new EnergyFlowInputVO(
                dataForDashboard.getACLoadP(),
                dataForDashboard.getBackupLoadP(),
                supportsConnectedPV,
                dataForDashboard.getPVP(),
                dataForDashboard.getThirdPartyInverterP(),
                dataForDashboard.getGridStatus(),
                dataForDashboard.getGridP(),
                configuredWithBatteries,
                batterySummary != null && batterySummary.getP() != null ? new Watt(batterySummary.getP()) : null,
                batterySummary != null ? batterySummary.getStatus() : null,
                batterySummary != null ? batterySummary.getSoC() : null,
                dataForDashboard.getCTComms() != null ? dataForDashboard.getCTComms() : false
        );

        return Build(raw);
    }

    public static boolean BigAdjustment(Watt power, Watt power2) {
        return power.subtract(power2).abs().greaterThan(new Watt(Double.valueOf(EnergyFlowCoherentInputVO.SignificantImbalanceThreshold)));
    }

    public static Tuple2<EnergyFlowInputVO, EnergyFlowVO> Build(EnergyFlowInputVO raw, EnergyFlowCoherentInputVO input) {

        // CASE A: On Grid
        if (input.isOnGrid()) {
            if (!input.CtComms) {
                input.Issues.add(EnergyFlowIssue.OnGridButNoCt);
            }

            // Please note the comparison is with 'raw', not with 'input'
            if (getValue(raw, EnergyFlowInputVO::getGridNegativeIsImportW, Watt::getValue) != null
                    && BigAdjustment(input.getCalculatedGridW(), raw.getGridNegativeIsImportW())) {
                input.Issues.add(EnergyFlowIssue.BigAdjustment);
            }

            // If equation doesn't balance then adjust to fit
            BalanceGrid(input);

            Watt gridNegativeIsImportW = raw.getGridStatus() == GridStatusValue.Disconnected ? null : input.GridNegativeIsImportW;
            EnergyFlowIssue[] distinctIssues = input.Issues.stream().distinct().toArray(EnergyFlowIssue[]::new);
            return new Tuple2<>(raw, new EnergyFlowVO(
                    input.ACLoadW,
                    input.BackupLoadW,
                    input.PVW,
                    input.ThirdPartyW,
                    input.HasBatteries,
                    gridNegativeIsImportW,
                    input.BatteryNegativeIsChargingW,
                    input.BatterySoC0to1,
                    distinctIssues
            ));
        }

        // CASE B: Offgrid
        // CASE B2: No battery data
        if (input.BatteryNegativeIsChargingW==null) {
            Watt diff = input.getTotalSourcePVW().subtract(input.getTotalLoadW()).abs();
            if (diff.greaterThan(new Watt(BigDecimal.valueOf(100)))) {
                input.Issues.add(EnergyFlowIssue.NoBatteriesOffgridPVLoadMismatch);
            }

            EnergyFlowIssue[] distinctIssues = input.Issues.stream().distinct().toArray(EnergyFlowIssue[]::new);
            return new Tuple2<>(raw, new EnergyFlowVO(
                    input.ACLoadW,
                    input.BackupLoadW,
                    input.PVW,
                    input.ThirdPartyW,
                    input.HasBatteries,
                    null,
                    null,
                    input.BatterySoC0to1,
                    distinctIssues
            ));
        }

        // CASE B1: With batteries
        Watt correctedBatteryNegativeIsChargingW = input.getTotalLoadW().subtract(input.getTotalSourcePVW());
        if (!correctedBatteryNegativeIsChargingW.equals(Watt.getValueOrZero(input.BatteryNegativeIsChargingW))) {
            input.Issues.add(EnergyFlowIssue.BalancingAdjustmentToBatteryPower);
        }

        // Please note the comparison is with 'raw', not with 'input'
        if (getValue(raw, EnergyFlowInputVO::getBatteryNegativeIsChargingW, Watt::getValue) != null
                && BigAdjustment(correctedBatteryNegativeIsChargingW, raw.getBatteryNegativeIsChargingW())) {
            input.Issues.add(EnergyFlowIssue.BigAdjustment);
        }

        EnergyFlowIssue[] distinctIssues = input.Issues.stream().distinct().toArray(EnergyFlowIssue[]::new);
        return new Tuple2<>(raw, new EnergyFlowVO(
                input.ACLoadW,
                input.BackupLoadW,
                input.PVW,
                input.ThirdPartyW,
                true,
                null,
                correctedBatteryNegativeIsChargingW,
                input.BatterySoC0to1,
                distinctIssues
        ));
    }

    private static void BalanceGrid(EnergyFlowCoherentInputVO input) {
        // If equation doesn't balance then adjust to fit
        if (input.getGridImbalanceW().equals(Watt.Zero)) {
            return;
        }

        // If gap is negative, then too much energy in system so remove to balance
        // If gap is positive, then not enough energy in system so add to balance
        Watt adjustment = input.getGridImbalanceW().lessThan(Watt.Zero) ? new Watt("-100") : Watt.Hecto;

        // Repeat until balanced, but use a circuit breaker to limit balancing to a number of passes (3)
        // and not create an infinite loop from bad numbers in unexpected circumstances
        int circuitBreaker = 0;
        while (!input.getGridImbalanceW().equals(Watt.Zero) && circuitBreaker <= 3) {
            circuitBreaker++;

            // Distribute gap over PV (if any) > Thirdparty PV (if any) > Battery (if charging or discharging) > Loads
            // Don't adjust zero value levers, dont create something from nothing (e.g. if no PV then don't add any)
            // Continue to apply adjustment until GridImbalanceW == 0

            // Only add to PV if > 0 or subtract from PV if > 100
            if (!input.getGridImbalanceW().equals(Watt.Zero) && (Watt.getValueOrZero(input.PVW).greaterThan(adjustment.getValue().compareTo(BigDecimal.ZERO) < 0 ? Watt.Hecto : Watt.Zero))) {
                input.PVW = input.PVW.add(adjustment);
                input.Issues.add(EnergyFlowIssue.BalancingAdjustmentToPV);
            }

            // Don't apply if adjustment would reduce grid to 0.
            if (!input.getGridImbalanceW().equals(Watt.Zero)
                    && Watt.getValueOrZero(input.GridNegativeIsImportW).getValue().compareTo(BigDecimal.ZERO) != 0
                    && Watt.getValueOrZero(input.GridNegativeIsImportW).getValue().compareTo(adjustment.getValue()) != 0) {
                input.GridNegativeIsImportW = input.GridNegativeIsImportW.subtract(adjustment);
                input.Issues.add(EnergyFlowIssue.BalancingAdjustmentToGridPower);
            }

            // Don't apply if adjustment would reduce battery to 0.
            if (!input.getGridImbalanceW().equals(Watt.Zero)
                    && Watt.getValueOrZero(input.BatteryNegativeIsChargingW).getValue().compareTo(BigDecimal.ZERO) != 0
                    && Watt.getValueOrZero(input.BatteryNegativeIsChargingW).getValue().compareTo(adjustment.getValue().negate()) != 0) {
                input.BatteryNegativeIsChargingW = input.BatteryNegativeIsChargingW.add(adjustment);
                input.Issues.add(EnergyFlowIssue.BalancingAdjustmentToBatteryPower);
            }

            // Only add to ThirdPartyW if > 0 or subtract from ThirdPartyW if > 100
            if (!input.getGridImbalanceW().equals(Watt.Zero) && (Watt.getValueOrZero(input.ThirdPartyW).greaterThan(adjustment.getValue().compareTo(BigDecimal.ZERO) < 0 ? Watt.Hecto : Watt.Zero))) {
                input.ThirdPartyW = input.ThirdPartyW.add(adjustment);
                input.Issues.add(EnergyFlowIssue.BalancingAdjustmentToThirdPartyPV);
            }

            // Dont touch loads on first cycle to avoid too large an adjustment being applied
            if (circuitBreaker == 1) {
                continue;
            }

            // Only add to Loads if > 0 or subtract from loads if > 100
            if (!input.getGridImbalanceW().equals(Watt.Zero) && input.ACLoadW.greaterThan(adjustment.getValue().compareTo(BigDecimal.ZERO) < 0 ? Watt.Zero : Watt.Hecto)) {
                input.ACLoadW = input.ACLoadW.subtract(adjustment);
                input.Issues.add(EnergyFlowIssue.BalancingAdjustmentToACLoad);
            }

            if (!input.getGridImbalanceW().equals(Watt.Zero) && input.BackupLoadW.greaterThan(adjustment.getValue().compareTo(BigDecimal.ZERO) < 0 ? Watt.Zero : Watt.Hecto)) {
                input.BackupLoadW = input.BackupLoadW.subtract(adjustment);
                input.Issues.add(EnergyFlowIssue.BalancingAdjustmentToBackupLoad);
            }
        }

        // If failed to balance, then just add difference to grid to force balance
        // If Grid was 0 this code path would not have been hit
        if (!input.getGridImbalanceW().equals(new Watt(BigDecimal.ZERO))) {
            input.GridNegativeIsImportW = input.GridNegativeIsImportW.subtract(input.getGridImbalanceW());
            input.Issues.add(EnergyFlowIssue.BalancingAdjustmentInsufficient);
        }
    }

    private static Tuple3<Watt, List<EnergyFlowIssue>, BigDecimal> getBattery(Boolean configuredWithBatteries, Watt batterPowerNegativeIsChargingW,
                                                                              BatteryStatusValue status, Double soc0to100) {
        BigDecimal soc0to1 = new BigDecimal(soc0to100 != null ? String.valueOf(soc0to100) : "0").divide(new BigDecimal("100"));

        if (status == null) {
            if (configuredWithBatteries != null && configuredWithBatteries) {
                List<EnergyFlowIssue> issues = new ArrayList<>();
                issues.add(EnergyFlowIssue.ConfiguredWithBatteriesTrueButNoBatteryData);
                return Tuple.of(new Watt(BigDecimal.ZERO), issues, soc0to1);
            } else {
                return Tuple.of(null, null, soc0to1);
            }
        }

        if (status == BatteryStatusValue.Disconnected) {
            if (configuredWithBatteries != null && configuredWithBatteries) {
                List<EnergyFlowIssue> issues = new ArrayList<>();
                issues.add(EnergyFlowIssue.ConfiguredWithBatteriesTrueButNoBatteryData);
                return Tuple.of(null, issues, soc0to1);
            } else {
                return Tuple.of(null, null, soc0to1);
            }
        }

        if (status == BatteryStatusValue.Disabled) {
            return Tuple.of(null, null, soc0to1);
        }

        List<EnergyFlowIssue> issues = new ArrayList<>();
        if (configuredWithBatteries != null && !configuredWithBatteries) {
            issues.add(EnergyFlowIssue.ConfiguredWithBatteriesFalseButBatteryDataPresent);
        }

        if (batterPowerNegativeIsChargingW == null || batterPowerNegativeIsChargingW.getValue() == null) {
            issues.add(EnergyFlowIssue.BatteryDataMissing);
        }

        switch (status) {
            case Charging:
                if (batterPowerNegativeIsChargingW == null || batterPowerNegativeIsChargingW.getValue() == null || batterPowerNegativeIsChargingW.getValue().compareTo(BigDecimal.ZERO) == 0) {
                    return Tuple.of(new Watt(BigDecimal.ZERO), issues, soc0to1);
                }
                if (batterPowerNegativeIsChargingW.getValue().compareTo(BigDecimal.ZERO) > 0) {
                    issues.add(EnergyFlowIssue.BatteryDataWrongDirection);
                }
                return Tuple.of(new Watt(BigDecimal.ZERO.subtract(batterPowerNegativeIsChargingW.getValue().abs())), issues, soc0to1);
            case Discharging:
                if (batterPowerNegativeIsChargingW == null || batterPowerNegativeIsChargingW.getValue() == null || batterPowerNegativeIsChargingW.getValue().compareTo(BigDecimal.ZERO) == 0) {
                    return Tuple.of(new Watt(BigDecimal.ZERO), issues, soc0to1);
                }
                if (batterPowerNegativeIsChargingW.getValue().compareTo(BigDecimal.ZERO) < 0) {
                    issues.add(EnergyFlowIssue.BatteryDataWrongDirection);
                }
                return Tuple.of(new Watt(batterPowerNegativeIsChargingW.getValue().abs()), issues, soc0to1);
            case Idle:
                if (batterPowerNegativeIsChargingW == null || batterPowerNegativeIsChargingW.getValue() == null || batterPowerNegativeIsChargingW.getValue().compareTo(BigDecimal.ZERO) == 0) {
                    return Tuple.of(new Watt(BigDecimal.ZERO), issues, soc0to1);
                }
                issues.add(EnergyFlowIssue.BatteryPowerButStatusIdle);
                return Tuple.of(batterPowerNegativeIsChargingW, issues, soc0to1);
            case Disconnected:
                if (batterPowerNegativeIsChargingW == null || batterPowerNegativeIsChargingW.getValue() == null || batterPowerNegativeIsChargingW.getValue().compareTo(BigDecimal.ZERO) == 0) {
                    return Tuple.of(null, issues, soc0to1);
                }
                if (batterPowerNegativeIsChargingW.getValue().abs().compareTo(BigDecimal.valueOf(PowerConsideredIdleW)) < 0) {
                    issues.add(EnergyFlowIssue.LowBatteryPowerMayBeInacurrate);
                    return Tuple.of(null, issues, soc0to1);
                }
                issues.add(EnergyFlowIssue.BatteryPowerButStatusDisconnected);
                return Tuple.of(batterPowerNegativeIsChargingW, issues, soc0to1);
            default:
                issues.add(EnergyFlowIssue.UnknownBatteryStatus);
                return Tuple.of(batterPowerNegativeIsChargingW, issues, soc0to1);
        }
    }

    private static Tuple2<Watt, EnergyFlowIssue> getGrid(Watt gridW, GridStatusValue gridStatus) {
        if (gridW == null ) {
            if (gridStatus == GridStatusValue.Disconnected || gridStatus == GridStatusValue.Idle) {
                return Tuple.of(null, null);
            }
            return Tuple.of(null, EnergyFlowIssue.IncoherentGridData);
        }

        if (gridW.asDecimal().compareTo(BigDecimal.ZERO) == 0) {
            if (gridStatus == GridStatusValue.Disconnected) {
                return Tuple.of(null, null);
            }
            if (gridStatus != GridStatusValue.Idle) {
                return Tuple.of(new Watt(BigDecimal.ZERO), EnergyFlowIssue.IncoherentGridData);
            }
            return Tuple.of(new Watt(BigDecimal.ZERO), null);
        }

        if (gridW.asDecimal().compareTo(BigDecimal.ZERO) > 0) {
            if (gridStatus != GridStatusValue.Export) {
                return Tuple.of(gridW, EnergyFlowIssue.IncoherentGridData);
            }
            return Tuple.of(gridW, null);
        } else {
            if (gridStatus != GridStatusValue.Import) {
                return Tuple.of(gridW, EnergyFlowIssue.IncoherentGridData);
            }
            return Tuple.of(gridW, null);
        }
    }
}