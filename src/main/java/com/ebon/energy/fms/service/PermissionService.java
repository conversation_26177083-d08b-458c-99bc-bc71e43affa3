package com.ebon.energy.fms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.entity.PermissionDO;
import com.ebon.energy.fms.domain.entity.RolePermissionDO;
import com.ebon.energy.fms.domain.vo.PermissionVO;
import com.ebon.energy.fms.mapper.primary.PermissionMapper;
import com.ebon.energy.fms.mapper.primary.RolePermissionMapper;
import com.ebon.energy.fms.util.PathPatternMatcher;
import com.ebon.energy.fms.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.StreamUtil.*;

@Slf4j
@Service
public class PermissionService {

    @Resource
    private PermissionMapper permissionMapper;

    @Resource
    private RolePermissionMapper rolePermissionMapper;

    public List<PermissionVO> getAllPermissions() {
        LambdaQueryWrapper<PermissionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PermissionDO::getStatus, true);
        List<PermissionDO> permissions = permissionMapper.selectList(queryWrapper);
        return buildPermissionTree(permissions);
    }

    public List<PermissionDO> getPermissions(List<String> codes) {
        LambdaQueryWrapper<PermissionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PermissionDO::getPermissionCode, codes);
        return permissionMapper.selectList(queryWrapper);
    }

    public List<String> getRolePermCodes(Integer roleId) {
        List<PermissionDO> permissions = permissionMapper.selectByRoleId(roleId);
        return mapList(permissions, PermissionDO::getPermissionCode);
    }

    public List<String> getCurrentUserPermCodes() {
        List<PermissionDO> permissions = permissionMapper.selectByUserId(RequestUtil.getLoginUserId());
        return mapList(permissions, PermissionDO::getPermissionCode);
    }

    public void saveRolePerms(Integer roleId, List<String> permCodes) {
        if (CollectionUtils.isEmpty(permCodes)) {
            return;
        }

        List<PermissionDO> permissions = getPermissions(permCodes);

        List<RolePermissionDO> rolePerms = mapList(permissions, e -> {
            RolePermissionDO rp = new RolePermissionDO();
            rp.setRoleId(roleId);
            rp.setPermissionId(e.getId());
            rp.setCreatedBy(RequestUtil.getLoginUserId());
            return rp;
        });
        rolePermissionMapper.batchInsert(rolePerms);
    }

    public void updateRolePerms(Integer roleId, List<String> permCodes) {
        if (CollectionUtils.isEmpty(permCodes)) {
            return;
        }

        // 查询当前角色已分配的权限code列表
        List<String> existPermissionCodes = getRolePermCodes(roleId);

        Set<String> newPermissionSet = new HashSet<>(permCodes);
        Set<String> exisPermissionSet = new HashSet<>(existPermissionCodes);

        // 需要新增的权限（新列表有但旧列表没有）
        List<String> permissionsToAdd = filterList(newPermissionSet, code -> !exisPermissionSet.contains(code));

        // 需要删除的权限（旧列表有但新列表没有）
        List<String> permissionsToDelete = filterList(exisPermissionSet, code -> !newPermissionSet.contains(code));

        // 执行批量操作新增
        if (CollectionUtils.isNotEmpty(permissionsToAdd)) {
            saveRolePerms(roleId, permissionsToAdd);
        }

        if (CollectionUtils.isNotEmpty(permissionsToDelete)) {
            deleteRolePerms(roleId, permissionsToDelete);
        }
    }

    public void checkPerm(String servletPath) {
        List<PermissionDO> permissions = permissionMapper.selectByUserId(RequestUtil.getLoginUserId());
        for (PermissionDO permission : permissions) {
            if (StringUtils.isBlank(permission.getUrl())) {
                continue;
            }

            String[] urls = permission.getUrl().split(",");
            for (String url : urls) {
                if (PathPatternMatcher.pathMatch(servletPath, url)) {
                    return;
                }
            }
        }

        throw new BizException(CommonErrorCodeEnum.UNAUTHORIZED);
    }

    /**
     * 批量删除角色权限
     */
    private void deleteRolePerms(Integer roleId, List<String> permCodes) {
        if (CollectionUtils.isEmpty(permCodes)) {
            return;
        }

        List<PermissionDO> permissions = getPermissions(permCodes);
        rolePermissionMapper.batchDeleteByRoleIdAndPermIds(roleId, mapList(permissions, PermissionDO::getId));
    }

    /**
     * 构建权限树形结构
     */
    private List<PermissionVO> buildPermissionTree(List<PermissionDO> permissions) {
        // 按ID分组，便于快速查找
        Map<Integer, PermissionDO> permissionMap = toMap(permissions, PermissionDO::getId);

        // 按父ID分组，便于构建层级关系
        Map<Integer, List<PermissionDO>> childrenMap = groupBy(permissions, PermissionDO::getParentId);

        // 构建树形结构
        List<PermissionVO> rootNodes = new ArrayList<>();

        // 先处理顶层节点（parentId为0的节点）
        permissions.stream()
                .filter(p -> p.getParentId() == 0)
                .forEach(rootPermission -> {
                    PermissionVO rootNode = createTreeNode(rootPermission, childrenMap, permissionMap);
                    rootNodes.add(rootNode);
                });

        return rootNodes;
    }

    /**
     * 递归创建树节点
     */
    private PermissionVO createTreeNode(PermissionDO permission,
                                        Map<Integer, List<PermissionDO>> childrenMap,
                                        Map<Integer, PermissionDO> permissionMap) {
        PermissionVO node = new PermissionVO();
        node.setId(permission.getId());
        node.setLabel(permission.getPermissionName());
        node.setKey(permission.getPermissionCode());
        node.setType(permission.getPermissionType());

        // 查找当前节点的子节点
        List<PermissionDO> children = childrenMap.getOrDefault(permission.getId(), Collections.emptyList());

        // 递归创建子节点
        children.forEach(child -> {
            PermissionVO childNode = createTreeNode(child, childrenMap, permissionMap);
            node.getChildren().add(childNode);
        });

        return node;
    }
}