package com.ebon.energy.fms.service.site;

import com.ebon.energy.fms.common.utils.Helper;
import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.config.PortalConfig;
import com.ebon.energy.fms.domain.entity.ProductDailyCachesDO;
import com.ebon.energy.fms.domain.vo.DataWithPermalinkVO;
import com.ebon.energy.fms.domain.vo.site.*;
import com.ebon.energy.fms.domain.vo.product.control.InstallationSpecification;
import com.ebon.energy.fms.domain.entity.ProductDailyHistoryDO;
import com.ebon.energy.fms.repository.ProductDbRepository;
import com.ebon.energy.fms.repository.impl.SiteRepository;
import com.ebon.energy.fms.service.SiteDeviceService;
import com.ebon.energy.fms.service.SiteService;
import com.ebon.energy.fms.service.factory.EnergyFlowFactory;
import com.ebon.energy.fms.service.factory.ISiteConfigurationAggregator;
import com.ebon.energy.fms.service.factory.SiteConfigurationAggregatorFactory;
import com.ebon.energy.fms.util.ConfigurationUtil;
import com.ebon.energy.fms.repository.impl.SpecificationRepository;
import com.ebon.energy.fms.mapper.third.ProductDailyCachesMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SiteEnergyService {

    private final SiteService siteService;

    private final SiteRepository siteRepository;

    private final SiteDeviceService siteDeviceService;

    private final SiteConfigurationAggregatorFactory siteConfigurationAggregatorFactory;
    
    private final SpecificationRepository specificationRepository;
    
    private final ProductDbRepository productDbRepository;


    public EnergyFlowDto getEnergyFlow(String siteId) {
        var exportLimitMessageThresholdW = ConfigurationUtil.getPortalConfig().getExportLimitMessageThresholdW();
        return getSiteEnergyFlow(siteId, exportLimitMessageThresholdW);
    }

    public BatteryStatusDto getBatteryStatus(String siteId) {
        var portalConfig = ConfigurationUtil.getPortalConfig();
        var globalCurrentGridExportLimit = portalConfig.getExportLimitMessageThresholdW();
        var numberOfConsecutiveDaysBatteriesMismatchedToDisabled = portalConfig.getNumberOfConsecutiveDaysBatteriesMismatchedToDisabled();
        var siteDevices = siteDeviceService.getSiteDevices(siteId);
        var siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(siteId, siteDevices);
        var allAbout = siteAggregator.processSiteOverview(siteId, siteDevices);
        if (allAbout.getDataForDashboard() == null) {
            return null;
        }
        var status = siteAggregator.processSiteBatteryStatus(
                allAbout,
                getSiteEnergyFlow(siteId, globalCurrentGridExportLimit),
                numberOfConsecutiveDaysBatteriesMismatchedToDisabled);

        if (status == null){
            return  null;
        }
        

        return  status;
    }

    public RenewableGaugeDto getRenewableGauge(String siteId) {
        var portalConfig = ConfigurationUtil.getPortalConfig();
        var siteDevices = siteDeviceService.getSiteDevices(siteId);
        var siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(siteId, siteDevices);
        var allAbout = siteAggregator.processSiteOverview(siteId, siteDevices);
        if (allAbout.getIsAcCoupledMode()) {
            return null;
        } else {
            return getRenewableGaugeInternal(siteId, null, siteDevices);
        }
    }


    public EnergyFlowDto getSiteEnergyFlow(String siteId, Integer globalCurrentGridExportLimit) {
        var siteDevices = siteDeviceService.getSiteDevices(siteId);
        var siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(siteId, siteDevices);
        var allAbout = siteAggregator.processSiteOverview(siteId, siteDevices);
        if (allAbout == null || allAbout.getDataForDashboard() == null) {
            return null;
        }

        var energyFlowExtended = EnergyFlowFactory.buildForViewModels(allAbout.getDataForDashboard(),
                allAbout.getHasBatteries(), allAbout.getHasSupportForConnectedPV());

        return Helper.getEnergyFlowFromParts(
                siteId,
                energyFlowExtended,
                siteDevices.stream().map(AllAboutDevice::getSpecification).collect(Collectors.toList()),
                allAbout.getDataForDashboard().getSiteExportLimitW(),
                globalCurrentGridExportLimit
        );
    }

    public RenewableGaugeDto getRenewableGaugeInternal(String publicSiteId, String loggedInUserId, List<AllAboutDevice> siteDevices) {
        try {
            ISiteConfigurationAggregator siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(publicSiteId, siteDevices);
            LocalDate todayLocalTime = siteRepository.getBclTimeZoneIdForSite(publicSiteId);
            LocalDate startOfToday = todayLocalTime;
            
            List<ProductWithDailyCache> productsWithDailies = new ArrayList<>();
            
            // For each device, populate productWithDailies from database
            for (AllAboutDevice device : siteDevices) {
                try {
                    InstallationSpecification installSpecs = specificationRepository.getInstallationSpecAsync(device.getSerialNumber());
                    
                    // Get 91 days of data (90 + 1 for today, which we will drop to avoid partial results)
                    LocalDate startDate = startOfToday.minusDays(90);
                    Timestamp startTime = Timestamp.valueOf(startDate.atStartOfDay());
                    Timestamp endTime = Timestamp.valueOf(startOfToday.atStartOfDay());
                    
                    var dailyCaches = productDbRepository.getNDayProductDailyCacheTrusted(
                            device.getSerialNumber(), startTime, endTime);
                    
                    // Filter out today to avoid partial results
                    var filteredCaches = dailyCaches.stream()
                            .filter(d -> d.getTime().isBefore(startOfToday.atStartOfDay()))
                            .collect(Collectors.toList());
                    
                    ProductWithDailyCache productWithDailies = new ProductWithDailyCache(
                            device.getSerialNumber(),
                            device,
                            installSpecs,
                            filteredCaches);
                    productsWithDailies.add(productWithDailies);
                } catch (Exception ex) {
                    log.error("Error collecting GetRenewableGauge data for device '{}' of site '{}'", device.getSerialNumber(), publicSiteId, ex);
                    return null;
                }
            }
            
            BigDecimal percent90DaysDouble = siteAggregator.processSiteRenewablePercent(publicSiteId, productsWithDailies);
            Integer percent90Days = percent90DaysDouble != null ? percent90DaysDouble.intValue() : null;
            
            // Calc the 30 day consumption
            LocalDate thirtyDaysAgo = startOfToday.minusDays(30);
            List<ProductWithDailyCache> thirtyDayBatch = new ArrayList<>();
            for (ProductWithDailyCache prodData : productsWithDailies) {
                var filteredThirtyDays = prodData.getDailyCaches().stream()
                        .filter(d -> !d.getTime().isBefore(thirtyDaysAgo.atStartOfDay()))
                        .collect(Collectors.toList());
                        
                thirtyDayBatch.add(new ProductWithDailyCache(
                        prodData.getSerialNumber(),
                        prodData.getBaseAboutDevice(),
                        prodData.getInstallationSpecification(),
                        filteredThirtyDays));
            }
            
            BigDecimal percent30DaysDouble = siteAggregator.processSiteRenewablePercent(publicSiteId, thirtyDayBatch);
            Integer percent30Days = percent30DaysDouble != null ? percent30DaysDouble.intValue() : null;
            
            // Calc the 7 Day consumption
            LocalDate sevenDaysAgo = startOfToday.minusDays(7);
            List<ProductWithDailyCache> sevenDayBatch = new ArrayList<>();
            for (ProductWithDailyCache prodData : thirtyDayBatch) {
                var filteredSevenDays = prodData.getDailyCaches().stream()
                        .filter(d -> !d.getTime().isBefore(sevenDaysAgo.atStartOfDay()))
                        .collect(Collectors.toList());
                        
                sevenDayBatch.add(new ProductWithDailyCache(
                        prodData.getSerialNumber(),
                        prodData.getBaseAboutDevice(),
                        prodData.getInstallationSpecification(),
                        filteredSevenDays));
            }
            
            BigDecimal percent07DaysDouble = siteAggregator.processSiteRenewablePercent(publicSiteId, sevenDayBatch);
            Integer percent07Days = percent07DaysDouble != null ? percent07DaysDouble.intValue() : null;
            
            // Calc the Today consumption
            List<ProductWithDailyCache> todayBatch = new ArrayList<>();
            for (ProductWithDailyCache prodData : thirtyDayBatch) {
                var filteredToday = prodData.getDailyCaches().stream()
                        .filter(d -> !d.getTime().isBefore(startOfToday.atStartOfDay()))
                        .collect(Collectors.toList());
                        
                todayBatch.add(new ProductWithDailyCache(
                        prodData.getSerialNumber(),
                        prodData.getBaseAboutDevice(),  
                        prodData.getInstallationSpecification(),
                        filteredToday));
            }
            
            BigDecimal percentTodayDouble = siteAggregator.processSiteRenewablePercent(publicSiteId, todayBatch);
            Integer percentToday = percentTodayDouble != null ? percentTodayDouble.intValue() : null;
            
            return new RenewableGaugeDto(publicSiteId, percentToday, percent07Days, percent30Days, percent90Days);
            
        } catch (Exception ex) {
            log.error("Error in getRenewableGaugeInternal for site '{}'", publicSiteId, ex);
            return null;
        }
    }

}
