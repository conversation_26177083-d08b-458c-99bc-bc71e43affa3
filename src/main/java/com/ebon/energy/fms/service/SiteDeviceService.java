package com.ebon.energy.fms.service;

import com.alibaba.fastjson.JSONObject;
import com.ebon.energy.fms.common.enums.ApplicationName;
import com.ebon.energy.fms.common.enums.PhaseRole;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.BackUpSupportStatus;
import com.ebon.energy.fms.common.enums.UniversalSettingId;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.common.utils.*;
import com.ebon.energy.fms.domain.entity.SiteDeviceDO;
import com.ebon.energy.fms.domain.entity.SiteInverterInfoDO;
import com.ebon.energy.fms.domain.po.DirectMethodSendPO;
import com.ebon.energy.fms.domain.po.HardwareSpecification;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.product.control.*;
import com.ebon.energy.fms.domain.vo.product.control.invert.DeviceInfoAndSettings;
import com.ebon.energy.fms.domain.vo.setting.LogicalSettingsChangeRequestDto;
import com.ebon.energy.fms.domain.vo.setting.PeerInfoDto;
import com.ebon.energy.fms.domain.vo.setting.SiteSettingsChangeRequestDto;
import com.ebon.energy.fms.domain.vo.site.*;
import com.ebon.energy.fms.domain.vo.telemetry.SystemStatus;
import com.ebon.energy.fms.mapper.third.SiteMapper;
import com.ebon.energy.fms.repository.ProductDbRepository;
import com.ebon.energy.fms.repository.RedbackRepository;
import com.ebon.energy.fms.repository.TelemetryRepository;
import com.ebon.energy.fms.repository.impl.SpecificationRepository;
import com.ebon.energy.fms.util.RequestUtil;
import com.ebon.energy.fms.util.TimeUtils;
import com.ebon.energy.fms.util.azure.IoTHubUtility;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.microsoft.azure.sdk.iot.service.methods.DirectMethodResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.ebon.energy.fms.util.StreamUtil.mapList;

@Slf4j
@Service
public class SiteDeviceService {
    
    @Resource
    private SettingChangeService settingChangeService;

    @Resource
    private DirectMethodsService directMethodsService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private SiteMapper siteMapper;
    
    @Resource
    private RedbackRepository redbackRepository;

    @Resource
    private SpecificationRepository specificationRepository;

    @Resource
    private ProductDbRepository productDbRepository;

    @Resource
    private TelemetryRepository telemetryRepository;

    @Resource
    private IoTHubUtility ioTHubUtility;

    private final String peersExecutionInterval = "0:00:10";

    private final String directMethodChangeHostnameCommandName = "changeHostname";

    @Value("${directMethodChangeHostnamePasswords}")
    private String directMethodChangeHostnamePasswords;

    public List<AllAboutDevice> getSiteDevices(String siteId) {
        Instant currentInstant = Instant.now();
        List<SiteDeviceDO> devices = siteMapper.selectSiteDevices(siteId);
        if (CollectionUtils.isEmpty(devices)) {
            throw new BizException("No site found with site id " + siteId);
        }

        return mapList(devices, e -> {
            SiteDeviceVO deviceVO = new SiteDeviceVO();
            BeanUtils.copyProperties(e, deviceVO);
            deviceVO.setLastSystemStatusReceivedUtc(TimeUtils.toZonedDateTime(e.getLastSystemStatusReceivedUtc()));
            deviceVO.setInstallationDateUtc(TimeUtils.toZonedDateTime(e.getInstallationDateUtc()));
            deviceVO.setSupportsLoadContributors(TimeUtils.toZonedDateTime(e.getSupportsLoadContributors()));

            AllAboutDevice device = null;
            try {
                device = getDeviceDetails(deviceVO);
            } catch (Exception ex) {
                log.error("Error running GetDeviceDetails for device '" + deviceVO.getSerialNumber() + "' of site '" + siteId + "'.", ex);

                LocalDate todaysDateInLocalTime = deviceVO.getBclTimeZoneId() != null ?
                        TimeZoneConverterUtil.getLocalDateFromBclTimeZone(currentInstant, deviceVO.getBclTimeZoneId())
                        : ZonedDateTime.now().toLocalDate();
                LocalDate supportsLoadContributorsSince = deviceVO.getSupportsLoadContributors();
                device = new AllAboutDevice(
                        deviceVO.getSerialNumber(),
                        null,
                        deviceVO,
                        null,
                        null,
                        todaysDateInLocalTime,
                        false,
                        false,
                        WattHour.Zero,
                        supportsLoadContributorsSince,
                        false,
                        WattHour.Zero,
                        null);
            }
         
            return device;
        });
    }

    public AllAboutDevice getDeviceDetails(SiteDeviceVO device) throws Exception {

        // 同步获取配置信息
        InstallationSpecification specification = specificationRepository.getInstallationSpecAsync(device.getSerialNumber());
        DeviceInfoAndSettings deviceInfoAndSettings = specificationRepository.getDeviceInfoAndSettings(null, device.getSerialNumber(), null);
        WattHour maximumPossiblePVOutputWh = DataServiceHelper.getDeviceMaximumPossiblePVOutputWh(productDbRepository, device.getSerialNumber());

        GenericInverterConfiguration conf = null;
        BatteryBackUpVO batteryBackUp = null;

        // 转换系统状态
        SystemStatus systemStatus = device.getLatestSystemStatus() != null
                ? JSONObject.parseObject(device.getLatestSystemStatus(), SystemStatus.class)
                : null;

        boolean isRoss1 = systemStatus == null
                || systemStatus.getOuijaBoard() == null
                || systemStatus.getOuijaBoard().getSoftwareVersion() == null
                || new RossVersion(systemStatus.getOuijaBoard().getSoftwareVersion()).isRoss1();

        boolean isBatteryMismatchProtectionEnabled = false;

        if (systemStatus != null) {
            if (isRoss1) {
                ElectricalConfigurationVO electrical = productDbRepository.getElectricalConfigurationRoss1(device.getSerialNumber());
                conf = UnifiedElectricalSettingsHelper.fromElectricalConfiguration(electrical).getConf();
                // Ross1默认不启用电池失配保护
            } else if (deviceInfoAndSettings != null && deviceInfoAndSettings.getDesired() != null) {
                ICommonSettingsReader reader = SettingsReaderProvider.get(deviceInfoAndSettings);
                conf = GenericInverterConfiguration.fromSettings(reader);
                isBatteryMismatchProtectionEnabled = reader.isBatteryMismatchProtectionEnabled();
            }
        }

        // 计算本地日期
        Instant currentInstant = Instant.now();
        LocalDate todaysDateInLocalTime = TimeZoneConverterUtil.getLocalDateFromBclTimeZone(currentInstant, device.getBclTimeZoneId());

        LocalDate supportsLoadContributorsSince = device.getSupportsLoadContributors();

        // 计算备用电池时间（同步实现）
        batteryBackUp = computeHoursOnBackup(
                device.getSerialNumber(),
                conf,
                systemStatus,
                specification);

        // 计算可用电池能量
        WattHour availableBatteryEnergyOverMinOffgridSoCWh = WattHour.Zero;
        if (systemStatus != null) {
            IRbSmartHybridSnapshot snapshot = Gen2SiteSnapshot.fromStatus(systemStatus);
            BatteryEnergyUtility batteryUtility = new BatteryEnergyUtility(snapshot, conf);
            WattHour currentlyAccessibleEnergy = batteryUtility.estimateCurrentlyAccessibleEnergyInBatteryWh(true);
            availableBatteryEnergyOverMinOffgridSoCWh = BatteryEnergyStatics.getUsableEnergyWh(currentlyAccessibleEnergy);
        }

        DataForDashboardVO deviceDataForDashboard = DataForDashboardVO.make(systemStatus);

        return new AllAboutDevice(
                device.getSerialNumber(),
                deviceDataForDashboard,
                device,
                specification,
                conf,
                todaysDateInLocalTime,
                specification != null && specification.isHasSolar(),
                (conf != null && conf.getHasBatteries())
                        || (deviceDataForDashboard != null && deviceDataForDashboard.getBatterySummary() != null && deviceDataForDashboard.getBatterySummary().getP() != null),
                maximumPossiblePVOutputWh,
                supportsLoadContributorsSince,
                isBatteryMismatchProtectionEnabled,
                availableBatteryEnergyOverMinOffgridSoCWh,
                batteryBackUp);
    }

    public void updateSettingsAndNotify(List<SiteInverterInfoVO> devices) {
        for (SiteInverterInfoVO device : devices) {
            HardwareSpecification model = SpecificationFactory.tryGet(device.getModel());
            if (model == null) {
                continue;
            }
            
            if (requiresHostNameDirectCommand(device, model)) {
                setHostName(RequestUtil.getPortolUserId(), device.getSerialNumber(), model);
            }
        }

        List<SetSiteCoordinatorDataOutDto> deviceRoles = mapList(devices, e -> {
            SetSiteCoordinatorDataOutDto outDto = new SetSiteCoordinatorDataOutDto();
            outDto.setRole(e.getSelectedRole());
            outDto.setSerialNumber(e.getSerialNumber());
            outDto.setHardwareSpecification(SpecificationFactory.tryGet(e.getModel()));
            return outDto;
        });

        for (SiteInverterInfoVO device : devices) {
            HardwareSpecification model = SpecificationFactory.tryGet(device.getModel());
            if (model == null) {
                continue;
            }
            
            if (supportsSiteCoordinator(device, model)) {
                try {
                    updateSettings(device.getSerialNumber(), device.getSelectedRole(), deviceRoles);
                } catch (Exception ex) {
                    log.error("Failed to update coordinator settings for device " + device.getSerialNumber(), ex);
                }
            }
        }
    }

    public void updateSettings(String serialNumber,
                               String role,
                               List<SetSiteCoordinatorDataOutDto> inverterData) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        // 第一步：清除所有peers设置
        SiteSettingsChangeRequestDto toSaveNoPeersDto = new SiteSettingsChangeRequestDto();
        toSaveNoPeersDto.setExecutionInterval(peersExecutionInterval);
        toSaveNoPeersDto.setInverterRole(null);
        toSaveNoPeersDto.setModbusServerEnable(true);
        toSaveNoPeersDto.setPeers(null);

        List<LogicalSettingsChangeRequestDto> requests = new ArrayList<>();
        LogicalSettingsChangeRequestDto request = new LogicalSettingsChangeRequestDto();
        request.setTargetSetting(UniversalSettingId.SITE_COORDINATOR);
        request.setTargetValue(objectMapper.writeValueAsString(toSaveNoPeersDto));
        requests.add(request);

        // 执行第一次设置更新（清除peers）
        settingChangeService.apply(serialNumber, null, requests, false);

        // 筛选符合条件的peers
        List<String> acceptedRoles = Arrays.asList(
                PhaseRole.Coordinator.name(),
                PhaseRole.Worker.name()
        );

        // 检查角色是否有效
        if (!acceptedRoles.contains(role)) {
            return;
        }

        List<SetSiteCoordinatorDataOutDto> peers = inverterData.stream()
                .filter(p -> !p.getSerialNumber().equals(serialNumber) && acceptedRoles.contains(p.getRole()))
                .filter(p -> p.getHardwareSpecification() != null && (p.getHardwareSpecification().isSinglePhaseInverter() ||
                        p.getHardwareSpecification().isSmartBatteryInverter() ||
                        p.getHardwareSpecification().isThreePhaseInverter() ||
                        p.getHardwareSpecification().isGen3SinglePhaseInverter()))
                .collect(Collectors.toList());

        // 第二步：设置新的peers和角色
        SiteSettingsChangeRequestDto toSaveDto = new SiteSettingsChangeRequestDto();
        toSaveDto.setExecutionInterval(peersExecutionInterval);
        toSaveDto.setInverterRole(role);
        toSaveDto.setModbusServerEnable(true);
        toSaveDto.setPeers(createPeersObjects(peers));

        List<LogicalSettingsChangeRequestDto> finalRequests = new ArrayList<>();
        LogicalSettingsChangeRequestDto finalRequest = new LogicalSettingsChangeRequestDto();
        finalRequest.setTargetSetting(UniversalSettingId.SITE_COORDINATOR);
        finalRequest.setTargetValue(objectMapper.writeValueAsString(toSaveDto));
        finalRequests.add(finalRequest);

        // 执行第二次设置更新（设置新配置）
        settingChangeService.apply( serialNumber, null, finalRequests, false);
    }

    public void setHostName(String loggedInUserId, String serialNumber, HardwareSpecification model) {
        CompletableFuture.runAsync(() -> {
            boolean success = false;

            DeviceVO device = deviceService.getLastDevice(serialNumber, ApplicationName.Ross.name());
            if (Objects.isNull(device) || StringUtils.isBlank(device.getDeviceId())) {
                log.error("Failed to find device Id for '" + serialNumber + "'");
                return;
            }

            // 获取设备ID
            String deviceId = device.getDeviceId();
            if (deviceId != null) {
                // 尝试所有密码
                for (String pwd : directMethodChangeHostnamePasswords.split(",")) {
                    // 构建请求体
                    String hostname = SiteCoordinatorHelper.createHostName(serialNumber, model.isGen3SinglePhaseInverter())
                            .replace(".local", "");
                    String methodBody = String.format("{\"hostname\": \"%s\", \"password\":\"%s\"}", hostname, pwd);

                    try {
                        //log.info(String.format("Sending Command '%s' to %s [%s]. Payload: %s", directMethodChangeHostnameCommandName, deviceId, serialNumber, methodBody));

                        DirectMethodResponse directResult = ioTHubUtility.sendDirectMethodWithPayload(device.getDeviceId(), directMethodChangeHostnameCommandName, methodBody).get();
                        String payload = directResult != null ? directResult.getPayloadAsJsonString() : null;

                        log.info(String.format("Hostname method for %s [%s] returned status code of %s Payload: %s", deviceId, serialNumber, directResult != null ? directResult.getStatus() : "null", payload));

                        if (directResult != null && directResult.getStatus() == 200) {
                            success = true;
                            //log.info(String.format("Hostname set successfully %s [%s]. Payload: %s", deviceId, serialNumber, methodBody));

                            // 判断是否需要重启
                            if (requiresRebootAfterDomainNameChange(model)) {
                                rebootRoss(serialNumber, loggedInUserId);
                            } else {
                                //log.info(String.format("Reboot skipped for %s [%s] as not required",deviceId, serialNumber));
                            }
                            break;
                        } else {
                            //log.info(String.format("Hostname method for %s [%s] failed, will not reboot and try next password.  %s Payload: %s", deviceId, serialNumber, directResult != null ? directResult.getStatus() : "null", payload));
                        }
                    } catch (Exception ex) {
                        log.error("Failed to set hostname for device {} serialNumber:{}.", deviceId, serialNumber, ex);
                    }
                }
            }

            // 所有密码尝试失败
            if (!success) {
                log.error("Device " + deviceId + " [" + serialNumber + "] failed to update hostname using all provided passwords.");
            }
        });
    }

    private boolean supportsSiteCoordinator(SiteInverterInfoVO device, HardwareSpecification model) {
        RossVersion rossVersion = new RossVersion(device.getRossVersion());
        return rossVersion.supportsSiteCoordinator() && !model.isGridTieInverter();
    }

    private boolean requiresHostNameDirectCommand(SiteInverterInfoVO device, HardwareSpecification model) {
        return supportsSiteCoordinator(device, model) && !model.isGen3SinglePhaseInverter();
    }

    private boolean requiresRebootAfterDomainNameChange(HardwareSpecification model) {
        // 实现判断是否需要重启的逻辑
        return !model.isGen3SinglePhaseInverter();
    }

    private void rebootRoss(String serialNumber, String loggedInUserId) {
        DeviceVO device = deviceService.getLastDevice(serialNumber, ApplicationName.Watchdog.name());
        if (Objects.isNull(device) || StringUtils.isBlank(device.getDeviceId())) {
            throw new BizException("Failed to find device Id for '" + serialNumber + "'");
        }

        try {
            String res = ioTHubUtility.sendRebootCommandAsync(device.getDeviceId()).get();
            log.info("Rebooted device {} [{}] with result of {}",
                    device.getDeviceId(), serialNumber, res);
        } catch (Exception ex) {
            log.error("Failed to reboot Ross on for {} [{}]. {}", device.getDeviceId(), serialNumber, ex);
        }
    }

    private Map<String, PeerInfoDto> createPeersObjects(List<SetSiteCoordinatorDataOutDto> peers) {
        Map<String, PeerInfoDto> result = new HashMap<>();
        for (SetSiteCoordinatorDataOutDto peer : peers) {
            String serialNumber = peer.getSerialNumber();
            PeerInfoDto peerInfo = new PeerInfoDto();
            peerInfo.setHostname(SiteCoordinatorHelper.createHostName(serialNumber, peer.getHardwareSpecification().isGen3SinglePhaseInverter()));
            result.put(serialNumber, peerInfo);
        }
        return result;
    }

    private BatteryBackUpVO computeHoursOnBackup(
            String serialNumber,
            GenericInverterConfiguration config,
            SystemStatus systemStatus,
            HardwareFirmwareSpecification specification) throws Exception {

        if (config == null || systemStatus == null || !config.getHasBatteries()) {
            return new BatteryBackUpVO(
                    null,
                    BackUpSupportStatus.NoBatteries,
                    specification != null && specification.isSupportsConnectedPV(),
                    serialNumber
            );
        }

        IRbSmartHybridSnapshot snapshot = Gen2SiteSnapshot.fromStatus(systemStatus);
        BatteryEnergyUtility batteryUtility = new BatteryEnergyUtility(snapshot, config);

        // 计算可用电池能量（Wh）
        WattHour availableBatteryEnergyWh = BatteryEnergyStatics.getUsableEnergyWh(
                batteryUtility.estimateCurrentlyAccessibleEnergyInBatteryWh(true)
        );

        // 计算时间范围（当前时间前1小时）
        Instant now = Instant.now();
        Instant oneHourAgo = now.minus(Duration.ofHours(1));

        // 获取过去一小时的平均功率数据（同步实现，需确保接口返回同步结果）
        SolarAndBackupPairVO lastHourStats = telemetryRepository.getDeviceAverageBackupAndSolarPowerBetween(
                serialNumber, oneHourAgo, now
        );

        if (lastHourStats != null) {
            Watt averageBackupPowerW = lastHourStats.getAverageBackupP();
            Watt nonTrivialThresholdW = new Watt(40);

            // 忽略小于阈值的备用功率
            if (averageBackupPowerW.abs().compareTo(nonTrivialThresholdW) > 0) {
                Watt averageSolarPowerW = Watt.Zero;
                if (specification != null && specification.isSupportsConnectedPV()) {
                    averageSolarPowerW = lastHourStats.getAverageSolarP();
                }

                // 计算电池需要提供的净功率（Backup - Solar）
                Watt netBatteryPowerW = averageBackupPowerW.subtract(averageSolarPowerW);

                if (netBatteryPowerW.compareTo(Watt.Zero) > 0) {
                    // 计算备用小时数（可用能量 / 净功率）
                    WattHour hoursOfBackup = availableBatteryEnergyWh.divide(netBatteryPowerW.getValue(), 2, BigDecimal.ROUND_HALF_UP);

                    BackUpSupportStatus supportStatus = averageSolarPowerW.compareTo(Watt.Zero) > 0
                            ? BackUpSupportStatus.PVAndBatteryMeetingBackupLoad
                            : BackUpSupportStatus.BatteryMeetingBackupLoad;

                    return new BatteryBackUpVO(
                            hoursOfBackup.getValue().doubleValue(),
                            supportStatus,
                            specification.isSupportsConnectedPV(),
                            serialNumber
                    );
                } else {
                    return new BatteryBackUpVO(
                            null,
                            BackUpSupportStatus.PVMeetingBackupLoad,
                            specification.isSupportsConnectedPV(),
                            serialNumber
                    );
                }
            }
        }

        return new BatteryBackUpVO(
                0.0,
                BackUpSupportStatus.NoBackup,
                specification != null && specification.isSupportsConnectedPV(),
                serialNumber
        );
    }
}
