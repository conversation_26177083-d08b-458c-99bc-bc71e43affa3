package com.ebon.energy.fms.repository.impl;

import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.mapper.third.SiteMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.*;

@Service
@RequiredArgsConstructor
public class SiteRepository {

    private final SiteMapper siteMapper;

    public LocalDate getBclTimeZoneIdForSite(String publicSiteId) {
        var bclTimeZoneId = siteMapper.getBclTimeZoneIdForSite(publicSiteId);

        if (bclTimeZoneId == null || bclTimeZoneId.isEmpty()) {
            throw new IllegalArgumentException("No time zone configured for given site");
        }

        // NOTE: 使用TimeZoneConverterUtil将BCL时区转换为ZoneId
        // 这类似于C#中的TimeZoneConverter.TZConvert.WindowsToIana
        var timeZone = TimeZoneConverterUtil.convertBclOrIanaToDateTimeZone(bclTimeZoneId);

        if (timeZone == null) {
            throw new IllegalArgumentException("No valid time zone configured for given site");
        }

        // 获取当前时间并转换为指定时区的本地日期
        // 这类似于C#中的ZonedClock和GetCurrentDate
        Instant currentInstant = Instant.now();
        ZonedDateTime zonedDateTime = currentInstant.atZone(timeZone);

        return zonedDateTime.toLocalDate();
    }

    public  GetSiteProducts



}
