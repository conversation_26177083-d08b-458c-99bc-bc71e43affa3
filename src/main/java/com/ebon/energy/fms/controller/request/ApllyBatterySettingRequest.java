package com.ebon.energy.fms.controller.request;

import com.ebon.energy.fms.common.enums.BatteryModelEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.vo.ElectricalViewModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ApllyBatterySettingRequest  {

    private String sn;

    private BatterySettingsChangeRequest settings;

    /**
     * Ross1 device model
     */
    private ElectricalViewModel model;

    /**
     * 验证电气配置
     *
     * @param model 电气视图模型
     * @throws BizException 当验证失败时抛出包含所有错误信息的异常
     */
    public void validateElectricalConfiguration(ElectricalViewModel model) {
        List<String> errors = new ArrayList<>();

        if (model.getBatteryModel() != BatteryModelEnum.None) {
            // 电池容量验证
            if (model.getBatteryCapacity() != null) {
                if (model.getBatteryCapacity() < 1) {
                    errors.add("BatteryCapacity: Capacity (Ah) cannot be 0 or less.");
                } else if (model.getBatteryCapacity() > 9999) {
                    errors.add("BatteryCapacity: Capacity (Ah) cannot be more than 4 digits.");
                }
            }

            // 电池单元数量验证
            if (model.getBatteryUnit() != null) {
                if (model.getBatteryUnit() < 1) {
                    errors.add("BatteryUnit: Number of battery modules cannot be 0 or less.");
                } else if (model.getBatteryUnit() > 99) {
                    errors.add("BatteryUnit: Number of battery modules cannot be more than 2 digits.");
                }
            }

            // 电池充电电压验证
            if (model.getBatteryChargeVoltage() != null) {
                if (model.getBatteryChargeVoltage() < 40 || model.getBatteryChargeVoltage() > 60) {
                    errors.add("BatteryChargeVoltage: Charge Max Voltage must be within the range [40, 60].");
                }
            }

            // 电池充电电流验证
            if (model.getBatteryChargeCurrent() != null) {
                if (model.getBatteryChargeCurrent() < 0 || model.getBatteryChargeCurrent() > 85) {
                    errors.add("BatteryChargeCurrent: Charge Max Current must be within the range [0, 85].");
                }
            }

            // 电池放电电压验证
            if (model.getBatteryDischargeVoltage() != null) {
                if (model.getBatteryDischargeVoltage() < 40 || model.getBatteryDischargeVoltage() > 60) {
                    errors.add("BatteryDischargeVoltage: Discharge Max Voltage must be within the range [40, 60].");
                }
            }

            // 电池放电电流验证
            if (model.getBatteryDischargeCurrent() != null) {
                if (model.getBatteryDischargeCurrent() < 0 || model.getBatteryDischargeCurrent() > 100) {
                    errors.add("BatteryDischargeCurrent: Discharge Max Current must be within the range [0, 100].");
                }
            }

            // 浮充设置验证
            if (Boolean.TRUE.equals(model.getApplyFloatSettings())) {
                if (model.getBatteryFloatVoltage() != null) {
                    if (model.getBatteryFloatVoltage() < 40 || model.getBatteryFloatVoltage() > 60) {
                        errors.add("BatteryFloatVoltage: Float voltage must be within the range [40, 60].");
                    }
                }
                if (model.getBatteryFloatCurrent() != null) {
                    if (model.getBatteryFloatCurrent() < 0 || model.getBatteryFloatCurrent() > 5) {
                        errors.add("BatteryFloatCurrent: Float current must be within the range [0, 5].");
                    }
                }
            }

            // 最小SoC验证
            if (model.getBatteryMinimumSoC() != null) {
                if (model.getBatteryMinimumSoC() < 0 || model.getBatteryMinimumSoC() > 100) {
                    errors.add("BatteryMinimumSoC: Minimum SoC must be within the range [0, 100].");
                }
            }

            // 离网最小SoC验证
            if (model.getBatteryMinimumSoCOffGrid() != null) {
                if (model.getBatteryMinimumSoCOffGrid() < 0 || model.getBatteryMinimumSoCOffGrid() > 100) {
                    errors.add("BatteryMinimumSoCOffGrid: Minimum SoC offgrid must be within the range [0, 100].");
                }
            }
        }

        // 功率因数验证
        if (Boolean.TRUE.equals(model.getLeading())) {
            if (model.getLeadingPowerFactor() != null) {
                if (model.getLeadingPowerFactor() < 0.8 || model.getLeadingPowerFactor() > 1) {
                    errors.add("LeadingPowerFactor: Power factor is invalid.");
                }
            }
        } else {
            if (model.getLaggingPowerFactor() != null) {
                if (model.getLaggingPowerFactor() < 0.8 || model.getLaggingPowerFactor() >= 1) {
                    errors.add("LaggingPowerFactor: Power factor is invalid.");
                }
            }
        }

        // 移动网络验证
        if (Boolean.TRUE.equals(model.getEnableMobileNetwork()) &&
            (model.getMobileNetworkProviderProfile() == null || model.getMobileNetworkProviderProfile().trim().isEmpty())) {
            errors.add("MobileNetworkProviderProfile: Mobile Network Provider Profile is mandatory if Mobile Network is enabled.");
        }

        // 站点限制功率验证
        if (model.getSiteLimitW() != null && model.getProductDefaults() != null) {
            if (model.getSiteLimitW() < model.getProductDefaults().getMinimumSiteExportWatts()) {
                errors.add(String.format("LimitExportPower: Please enter a value greater than or equal to %d.",
                    model.getProductDefaults().getMinimumSiteExportWatts()));
            }

            if (model.getSiteLimitW() > model.getProductDefaults().getMaximumSiteExportWatts()) {
                errors.add(String.format("LimitExportPower: Please enter a value less than or equal to %d.",
                    model.getProductDefaults().getMaximumSiteExportWatts()));
            }
        }

        // 如果有验证错误，抛出异常
        if (!errors.isEmpty()) {
            String errorMessage = String.join("; ", errors);
            throw new BizException("Validation failed: " + errorMessage);
        }
    }
}
