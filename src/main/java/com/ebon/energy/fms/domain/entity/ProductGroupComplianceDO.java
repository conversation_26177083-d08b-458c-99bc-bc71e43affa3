package com.ebon.energy.fms.domain.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class ProductGroupComplianceDO {

    @JsonProperty("GroupEventId")
    private Integer groupEventId;

    @JsonProperty("RedbackProductSn")
    private String redbackProductSn;

    @JsonProperty("IsCompliant")
    private Boolean isCompliant = false;

    @JsonProperty("ReceivedTimeUTC")
    private Timestamp receivedTimeUTC;


}
