package com.ebon.energy.fms.domain.vo.site;

import com.ebon.energy.fms.common.enums.GridStatusEnum;
import com.ebon.energy.fms.common.enums.SystemStatusEnum.*;
import com.ebon.energy.fms.common.utils.TimeZoneConverterUtil;
import com.ebon.energy.fms.common.utils.Version;
import com.ebon.energy.fms.domain.vo.*;
import com.ebon.energy.fms.domain.vo.telemetry.*;
import com.ebon.energy.fms.domain.vo.telemetry.Error;
import com.ebon.energy.fms.util.TimeUtils;

import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Generation 2逆变器抽象类
 *
 * 此类包含从SystemStatus构造时提取的数据属性。
 * 另一种方案是使用private SystemStatus _ss;，并通过只读属性访问_ss的数据。
 * 选择保留属性的原因：
 *  * 访问更快
 *  * 数据异常时可在构造时发现，而非访问时
 * 注意：这些问题可通过_ss方案解决，底层数据存储（字段 vs 单一_ss）可能会改变。
 *
 * 名称来源：Paul喜欢这个名字。
 */
public class Gen2SiteSnapshot implements IRbSmartHybridSnapshot {
    public static final String MAIN_CIRCUIT_NAME = "Main";
    public static final String BACKUP_CIRCUIT_NAME = "Backup";

    public Gen2SiteSnapshot(SystemStatus ss) {
        if (ss == null) {
            throw new NullPointerException("SystemStatus cannot be null");
        }

        this.serialNumber = ss.getInverter() != null ? ss.getInverter().getInverterSN() : null;
        this.modelName = ss.getInverter() != null ? ss.getInverter().getModelName() : null;
        this.firmwareVersion = ss.getInverter() != null ? ss.getInverter().getFirmwareVersion() : null;
        this.timestamp = Instant.ofEpochSecond(ss.getEpoch());
        this.timestampLocal = TimeZoneConverterUtil.getLocalDateFromBclTimeZone(ss.getOuijaBoard() != null ? ss.getOuijaBoard().getWindowsTime().toInstant() : null,
                ss.getOuijaBoard() != null ? ss.getOuijaBoard().getTimeZone() : null);

        // 解析Ross版本
        String versionString = Optional.ofNullable(ss.getOuijaBoard())
                .map(ob -> ob.getSoftwareVersion())
                .orElse("")
                .replace("ROSS v", "")
                .replace(",", ".")
                .replace(" ", "");
        versionString = Pattern.compile("[^0-9.]").matcher(versionString).replaceAll("");

        Version rossVersion;
        try {
            rossVersion = new Version(versionString);
        } catch (IllegalArgumentException e) {
            rossVersion = new Version(0, 0, 0, 0);
        }
        this.rossVersion = rossVersion;

        this.osVersion = ss.getOuijaBoard() != null ? ss.getOuijaBoard().getOSVersion() : null;

        // 解析电网状态
        GridStatusValue gridStatusValue = Optional.ofNullable(ss.getGrid())
                .map(GridStatus::getStatus)
                .orElse(GridStatusValue.Idle);
        GridStatusEnum gridStatus;
        switch (gridStatusValue) {
            case Import:
                gridStatus = GridStatusEnum.Import;
                break;
            case Export:
                gridStatus = GridStatusEnum.Export;
                break;
            case Disconnected:
                gridStatus = GridStatusEnum.Disconnected;
                break;
            case Idle:
            default:
                gridStatus = GridStatusEnum.Idle;
                break;
        }

        // 单位转换工具方法
        Function<Double, WattHour> energyWhFromKWh = (energyInKWh) ->
                new WattHour(BigDecimal.valueOf(energyInKWh != null ? energyInKWh * 1000 : 0));
        Function<Double, Watt> powerW = (power) ->
                new Watt(BigDecimal.valueOf(power != null ? power : 0));
        Function<Double, Volt> voltageV = (voltage) ->
                new Volt(BigDecimal.valueOf(voltage != null ? voltage : 0));
        Function<Double, Ampere> currentA = (current) ->
                new Ampere(BigDecimal.valueOf(current != null ? current : 0));

        // 初始化电网信息
        Grid grid = new Grid(
                "Grid",
                powerW.apply(ss.getGrid() != null ? ss.getGrid().getP() : null),
                voltageV.apply(ss.getGrid() != null ? ss.getGrid().getV() : null),
                currentA.apply(ss.getGrid() != null ? ss.getGrid().getI() : null),
                BigDecimal.valueOf(ss.getGrid() != null && ss.getGrid().getPowerFactor() != null ? ss.getGrid().getPowerFactor() : 0),
                BigDecimal.valueOf(ss.getGrid() != null && ss.getGrid().getF() != null ? ss.getGrid().getF() : 0),
                gridStatus,
                energyWhFromKWh.apply(ss.getGrid() != null ? ss.getGrid().getDayTotalImportE() : null),
                energyWhFromKWh.apply(ss.getGrid() != null ? ss.getGrid().getDayTotalExportE() : null),
                energyWhFromKWh.apply(ss.getGrid() != null ? ss.getGrid().getAllTimeTotalImportE() : null),
                energyWhFromKWh.apply(ss.getGrid() != null ? ss.getGrid().getAllTimeTotalExportE() : null)
        );
        this.grid = grid;

        // 初始化光伏电路
        List<BasicCircuit> pvs = new ArrayList<>();
        if (ss.getPV() != null && ss.getPV().getPVs() != null) {
            int index = 1;
            for (PV pv : ss.getPV().getPVs()) {
                pvs.add(new BasicCircuit(
                        "PV" + index++,
                        powerW.apply(pv.getP()),
                        voltageV.apply(pv.getV()),
                        currentA.apply(pv.getI())
                ));
            }
        }
        this.pvs = Collections.unmodifiableList(pvs);

        // 初始化太阳能阵列
        SolarArray solarArray = new SolarArray(
                powerW.apply(ss.getPV() != null ? ss.getPV().getP() : null),
                new WattHour(BigDecimal.valueOf(ss.getPV() != null && ss.getPV().getDayTotalE() != null ? ss.getPV().getDayTotalE() * 1000 : 0)),
                energyWhFromKWh.apply(ss.getPV() != null ? ss.getPV().getAllTimeTotalE() : null)
        );
        this.solarArray = solarArray;

        // 初始化第三方逆变器
        this.thirdPartyInverter = ss.getThirdPartyInverter() != null ? new SolarArray(
                powerW.apply(ss.getThirdPartyInverter().getP()),
                new WattHour(BigDecimal.valueOf(ss.getThirdPartyInverter().getDayTotalE() != null ? ss.getThirdPartyInverter().getDayTotalE() * 1000 : 0)),
                energyWhFromKWh.apply(ss.getThirdPartyInverter().getAllTimeTotalE())
        ) : null;

        // 初始化逆变器视角的电池存储
        EnergyStore energyStoreAsSeenByInverter = null;
        if (ss.getInverter() != null && ss.getInverter().getBatteryMeasurements() != null) {
            BatteryStatus batteryData = ss.getInverter().getBatteryMeasurements();
            energyStoreAsSeenByInverter = new EnergyStore(
                    "Battery (Inv)",
                    powerW.apply(batteryData.getP()),
                    voltageV.apply(batteryData.getV()),
                    currentA.apply(batteryData.getI()),
                    new WattHour(BigDecimal.valueOf(Optional.ofNullable(batteryData.getDayTotalInputE()).orElse(0.0) * 1000)),
                    new WattHour(BigDecimal.valueOf(Optional.ofNullable(batteryData.getDayTotalOutputE()).orElse(0.0) * 1000)),
                    BigDecimal.valueOf(batteryData.getSoC() != null ? batteryData.getSoC() / 100 : 0),
                    BigDecimal.valueOf(batteryData.getSOH() != null ? batteryData.getSOH() / 100 : 0),
                    new Capacity(batteryData.getCapacity() != null ? batteryData.getCapacity() : 0),
                    new WattHour(batteryData.getRatedCapacity()),
                    getMinMax(batteryData.getBatteries(), Battery::getMaxChargeCurrent),
                    getMinMax(batteryData.getBatteries(), Battery::getMaxDischargeCurrent),
                    batteryData.getBatteries() != null ? batteryData.getBatteries().size() : 0,
                    batteryData.getBatteryVoltageType() != null ? batteryData.getBatteryVoltageType() : BatteryVoltageType.Unknown,
                    voltageV.apply(batteryData.getMinCellVoltage()),
                    voltageV.apply(batteryData.getMaxCellVoltage()),
                    batteryData.getStatus()
            );
        }
        this.energyStoreAsSeenByInverter = energyStoreAsSeenByInverter;

        // 初始化电池存储
        EnergyStore energyStore = null;
        if (ss.getBattery() != null) {
            BatteryStatus batteryData = ss.getBattery();
            energyStore = new EnergyStore(
                    "Battery",
                    powerW.apply(batteryData.getP()),
                    voltageV.apply(batteryData.getV()),
                    currentA.apply(batteryData.getI()),
                    new WattHour(BigDecimal.valueOf(batteryData.getDayTotalInputE() != null ? batteryData.getDayTotalInputE() * 1000 : 0)),
                    new WattHour(BigDecimal.valueOf(batteryData.getDayTotalOutputE() != null ? batteryData.getDayTotalOutputE() * 1000 : 0)),
                    BigDecimal.valueOf(batteryData.getSoC() != null ? batteryData.getSoC() / 100 : 0),
                    BigDecimal.valueOf(batteryData.getSOH() != null ? batteryData.getSOH() / 100 : 0),
                    new Capacity(batteryData.getCapacity() != null ? batteryData.getCapacity() : 0),
                    new WattHour(batteryData.getRatedCapacity()),
                    getMinMax(batteryData.getBatteries(), Battery::getMaxChargeCurrent),
                    getMinMax(batteryData.getBatteries(), Battery::getMaxDischargeCurrent),
                    batteryData.getBatteries() != null ? batteryData.getBatteries().size() : 0,
                    energyStoreAsSeenByInverter != null ? energyStoreAsSeenByInverter.getVoltageType() : BatteryVoltageType.Unknown,
                    voltageV.apply(batteryData.getMinCellVoltage()),
                    voltageV.apply(batteryData.getMaxCellVoltage()),
                    batteryData.getStatus()
            );
        }
        this.energyStore = energyStore;

        this.inverterReportsConnectedBattery = !hasNoBattery(ss);

        // 初始化电池柜
        this.batteryCabinet = ss.getBatteryCabinet() != null ?
                new BatteryCabinet(new Celsius(ss.getBatteryCabinet().getTemperature())) : null;

        // 初始化电路
        BigDecimal backupLoadDayTotalWh = new BigDecimal(ss.getBackupLoad() != null ? Optional.ofNullable(ss.getBackupLoad()).map(it -> it.getDayTotalE()).orElse(0.0) * 1000 : 0);
        BigDecimal acLoadDayTotalWh = new BigDecimal(ss.getACLoad() != null ? Optional.ofNullable(ss.getACLoad()).map(it ->it.getDayTotalE()).orElse(0.0) * 1000 : 0)
                .subtract(backupLoadDayTotalWh)
                .max(BigDecimal.ZERO);

        List<IUnidirectionalCircuit> circuits = Arrays.asList(
                new UnidirectionalCircuit(
                        MAIN_CIRCUIT_NAME,
                        powerW.apply(ss.getACLoad() != null ? ss.getACLoad().getP() : null),
                        voltageV.apply(ss.getACLoad() != null ? ss.getACLoad().getV() : null),
                        currentA.apply(ss.getACLoad() != null ? ss.getACLoad().getI() : null),
                        BigDecimal.valueOf(ss.getACLoad() != null && ss.getACLoad().getF() != null ? ss.getACLoad().getF() : 0),
                        new WattHour(acLoadDayTotalWh)
                ),
                new UnidirectionalCircuit(
                        BACKUP_CIRCUIT_NAME,
                        powerW.apply(ss.getBackupLoad() != null ? ss.getBackupLoad().getP() : null),
                        voltageV.apply(ss.getBackupLoad() != null ? ss.getBackupLoad().getV() : null),
                        currentA.apply(ss.getBackupLoad() != null ? ss.getBackupLoad().getI() : null),
                        BigDecimal.valueOf(ss.getBackupLoad() != null && ss.getBackupLoad().getF() != null ? ss.getBackupLoad().getF() : 0),
                        new WattHour(backupLoadDayTotalWh)
                )
        );
        this.circuits = Collections.unmodifiableList(circuits);

        // 初始化站点信息
        this.site = new SnapshotSite(new WattHour(BigDecimal.valueOf(ss.getACLoad() != null ? Optional.ofNullable(ss.getACLoad().getDayTotalE()).orElse(0.0) : 0)));

        // 逆变器模式
        this.inverterMode = ss.getInverter() != null ? ss.getInverter().getInverterMode() : null;
        this.inverterModePower = ss.getInverter() != null && ss.getInverter().getInverterModePower() != null ?
                powerW.apply(ss.getInverter().getInverterModePower()) : null;
        this.inverterModeRequested = ss.getInverter() != null ? ss.getInverter().getRequestedInverterMode() : null;
        this.inverterModePowerRequested = ss.getInverter() != null && ss.getInverter().getRequestedInverterModePower() != null ?
                powerW.apply(ss.getInverter().getRequestedInverterModePower()) : null;
        this.inverterWorkMode = ss.getInverter() != null ? ss.getInverter().getESWorkMode() : null;

        // 错误信息
        this.errors = getErrors(ss);

        // 逆变器本地时间
        this.inverterTimeLocal =  ss.getInverter() != null ? ss.getInverter().getInverterTime().toLocalDateTime() : null;
    }

    // 工厂方法
    public static IRbSmartHybridSnapshot fromStatus(SystemStatus ss) {
        return new Gen2SiteSnapshot(ss);
    }

    public static IRbSmartHybridSnapshot orNull(SystemStatus ss) {
        return ss != null ? new Gen2SiteSnapshot(ss) : null;
    }

    // 辅助方法：获取最小/最大值
    private static Ampere getMinMax(List<Battery> batteries, Function<Battery, Double> valueExtractor) {
        if (batteries == null || batteries.isEmpty()) return null;
        Optional<Double> minMax = batteries.stream()
                .map(valueExtractor)
                .filter(Objects::nonNull)
                .min(Double::compareTo);
        return minMax.map(value -> new Ampere(BigDecimal.valueOf(value))).orElse(null);
    }

    // 辅助方法：转换错误
    private List<IError> getErrors(SystemStatus ss) {
        List<IError> errors = new ArrayList<>();
        addErrors(errors, ss.getSystemStatusErrors());
        addErrors(errors, ss.getBattery() != null ? ss.getBattery().getErrors() : null);
        addErrors(errors, ss.getInverter() != null ? ss.getInverter().getErrors() : null);
        addErrors(errors, ss.getInverter() != null && ss.getInverter().getBatteryMeasurements() != null ?
                ss.getInverter().getBatteryMeasurements().getErrors() : null);
        return Collections.unmodifiableList(errors);
    }

    private void addErrors(List<IError> errors, List<Error> errorList) {
        if (errorList != null) {
            errors.addAll(errorList.stream()
                    .map(e -> new SiteError(e.getErrorCode() != null ? e.getErrorCode() : 0, e.getDescription()))
                    .collect(Collectors.toList()));
        }
    }

    private boolean hasNoBattery(SystemStatus status) {
        BatteryStatusValue batteryStatus = Optional.ofNullable(status.getInverter())
                .map(InverterStatus::getBatteryMeasurements)
                .map(BatteryStatus::getStatus)
                .orElse(null);
        return batteryStatus == BatteryStatusValue.Disconnected;
    }

    /**
     * 获取系统ID
     */
    private final String serialNumber;

    /**
     * 获取快照的UTC时间戳
     *
     * 快照按间隔生成（当前为1分钟），快照中的值可能是最新的，
     * 但不意味着与状态时间戳属于同一秒。
     *
     * @see #getTimestampLocal()
     */
    private final Instant timestamp;

    /**
     * 获取生成此快照时站点的本地时间
     *
     * @see #getTimestamp()
     */
    private final LocalDate timestampLocal;

    /**
     * 获取不含 <see cref="SolarArray"/>、<see cref="Grid"/> 和 <see cref="EnergyStore"/> 的站点电路
     */
    private final Collection<IUnidirectionalCircuit> circuits;

    /**
     * 获取能量存储电路（通常为电池）
     *
     * EnergyStore的PowerW：充电为负，放电为正
     */
    private final IEnergyStore energyStore;

    /**
     * 获取逆变器视角的能量存储数据（同<see cref="ISiteSnapshot.getEnergyStore()"/>，但数据来自逆变器）
     */
    private final IEnergyStore energyStoreAsSeenByInverter;

    /**
     * 获取是否逆变器检测到电池已连接
     * 注意：即使此值为false，<see cref="ISiteSnapshot.getEnergyStore()"/>也可能不为null。
     * 2018年3月 - 添加此属性以支持21387开发任务，改进HasNoBattery以支持使用最新数据的仪表盘小组件（重构工作的一部分）
     */
    private final boolean inverterReportsConnectedBattery;

    /**
     * 获取太阳能阵列信息
     */
    private final ISolarArray solarArray;

    /**
     * 获取第三方逆变器信息
     */
    private final ISolarArray thirdPartyInverter;

    /**
     * 获取光伏电路的只读集合
     */
    private final Collection<IBasicCircuit> pvs;

    /**
     * 获取连接站点与电网的电路
     *
     * 电网进口日总量 <see cref="IBidirectionalCircuit.getDayTotalImportEWh()"/> 是家庭支付的电量，
     * 电网出口日总量 <see cref="IBidirectionalCircuit.getDayTotalExportEWh()"/> 是站点获得收益的电量。
     *
     * Grid的PowerW：进口为负，出口为正
     */
    private final IGrid grid;

    /**
     * 获取站点整体信息（非系统各部分的具体信息）
     */
    private final ISite site;

    /**
     * 获取ROSS软件版本
     */
    private final Version rossVersion;

    /**
     * 获取操作系统版本（实际为Windows IoT Core版本）
     * 示例：10.0.16299.64
     */
    private final String osVersion;

    /**
     * 获取当前错误列表
     */
    private final Collection<IError> errors;

    /**
     * 获取逆变器本地时间
     */
    private final LocalDateTime inverterTimeLocal;

    /**
     * 获取逆变器模式（可选）
     */
    private final InverterModeValue inverterMode;

    /**
     * 获取逆变器模式功率（可选）
     */
    private final Watt inverterModePower;

    /**
     * 获取请求的逆变器模式（可选）
     */
    private final InverterModeValue inverterModeRequested;

    /**
     * 获取请求的逆变器模式功率（可选）
     */
    private final Watt inverterModePowerRequested;

    /**
     * 获取逆变器工作模式（可选）
     */
    private final ESWorkModeValue inverterWorkMode;

    /**
     * 获取系统型号
     *
     * 示例值："SH5000", "SH4600"
     */
    private final String modelName;

    /**
     * 获取固件版本
     *
     * 示例值："12123", "181810"
     */
    private final String firmwareVersion;

    /**
     * 获取电池柜信息
     */
    private final IBatteryCabinet batteryCabinet;

    // Getter 方法
    public String getSerialNumber() {
        return serialNumber;
    }

    public Instant getTimestamp() {
        return timestamp;
    }

    public LocalDate getTimestampLocal() {
        return timestampLocal;
    }

    public Collection<IUnidirectionalCircuit> getCircuits() {
        return circuits;
    }

    public IEnergyStore getEnergyStore() {
        return energyStore;
    }

    public IEnergyStore getEnergyStoreAsSeenByInverter() {
        return energyStoreAsSeenByInverter;
    }

    public boolean isInverterReportsConnectedBattery() {
        return inverterReportsConnectedBattery;
    }

    public ISolarArray getSolarArray() {
        return solarArray;
    }

    public ISolarArray getThirdPartyInverter() {
        return thirdPartyInverter;
    }

    public Collection<IBasicCircuit> getPVs() {
        return pvs;
    }

    public IGrid getGrid() {
        return grid;
    }

    public ISite getSite() {
        return site;
    }

    public Version getRossVersion() {
        return rossVersion;
    }

    public String getOSVersion() {
        return osVersion;
    }

    public Collection<IError> getErrors() {
        return errors;
    }

    public LocalDateTime getInverterTimeLocal() {
        return inverterTimeLocal;
    }

    public InverterModeValue getInverterMode() {
        return inverterMode;
    }

    public Watt getInverterModePower() {
        return inverterModePower;
    }

    public InverterModeValue getInverterModeRequested() {
        return inverterModeRequested;
    }

    public Watt getInverterModePowerRequested() {
        return inverterModePowerRequested;
    }

    public ESWorkModeValue getInverterWorkMode() {
        return inverterWorkMode;
    }

    public String getModelName() {
        return modelName;
    }

    public String getFirmwareVersion() {
        return firmwareVersion;
    }

    public IBatteryCabinet getBatteryCabinet() {
        return batteryCabinet;
    }

}