package com.ebon.energy.fms.domain.vo;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 产品电气配置视图对象
 * 对应原始EF查询返回的匿名对象结构
 */
@Data
public class ProductElectricalConfigurationVO {

    /**
     * 产品拥有者ID
     */
    private String productOwnerId;

    /**
     * 配置ID
     */
    private Integer configurationId;

    /**
     * 配置内容（云端）
     */
    private String configurations;

    /**
     * 设备端配置内容
     */
    private String configurationsOnDevice;

    /**
     * 修改时间
     */
    private Timestamp modifiedDateTime;

    /**
     * 最后修改人ID
     */
    private String lastModifiedById;

    /**
     * 行版本
     */
    private byte[] rowVersion;
}