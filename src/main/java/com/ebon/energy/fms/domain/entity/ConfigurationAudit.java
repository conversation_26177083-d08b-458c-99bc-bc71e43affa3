package com.ebon.energy.fms.domain.entity;

import com.ebon.energy.fms.common.enums.ConfigurationType;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 配置审计实体类
 * 对应数据库表：ConfigurationsAudit
 */
@Data
@NoArgsConstructor
public class ConfigurationAudit {


    /**
     * 主键ID
     */
    @JsonProperty("Id")
    private String id;

    /**
     * 配置ID
     */
    @JsonProperty("ConfigurationId")
    private Integer configurationId;

    /**
     * Redback产品序列号
     */
    @JsonProperty("RedbackProductSn")
    private String redbackProductSn;

    /**
     * 配置类型
     * {@link ConfigurationType}
     */
    @JsonProperty("ConfigurationType")
    private Integer configurationType;

    /**
     * 配置信息
     */
    @JsonProperty("Configurations")
    private String configurations;

    /**
     * 设备上的配置信息
     */
    @JsonProperty("ConfigurationsOnDevice")
    private String configurationsOnDevice;

    /**
     * 修改时间
     */
    @JsonProperty("ModifiedDateTime")
    private LocalDateTime modifiedDateTime;

    /**
     * 行版本号
     * 来自Configuration表的rowversion字段
     */
    @NotNull
    @JsonProperty("RowVersion")
    private byte[] rowVersion;

    /**
     * 最后修改人ID
     */
    @JsonProperty("LastModifiedById")
    private String lastModifiedById;
}
