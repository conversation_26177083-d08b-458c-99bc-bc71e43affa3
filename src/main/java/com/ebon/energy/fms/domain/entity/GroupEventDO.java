// GroupEvent.java

package com.ebon.energy.fms.domain.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class GroupEventDO {

    @JsonProperty("Id")
    private Integer id;

    @JsonProperty("GroupEventType")
    private Integer groupEventType;

    @JsonProperty("When")
    private Timestamp when;

    @JsonProperty("ByUserId")
    private String byUserId;

    @JsonProperty("GroupId")
    private Integer groupId;

    @JsonProperty("TagName")
    private String tagName;

    @JsonProperty("FromWhat")
    private String fromWhat;

    @JsonProperty("ToWhat")
    private String toWhat;

    @JsonProperty("EventStatus")
    private Integer eventStatus = 0;

}
