package com.ebon.energy.fms.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 组电气配置类
 * 对应C#中的GroupElectricalConfiguration类
 */
@Data
public class GroupElectricalConfiguration {

    /**
     * 是否限制导出功率
     */
    @JsonProperty("IsLimitExportPower")
    private Boolean isLimitExportPower;

    /**
     * 限制导出功率值
     */
    @JsonProperty("LimitExportPower")
    private Integer limitExportPower;

    /**
     * 最小SoC是否已更改
     */
    @JsonProperty("IsMinimumSoCChanged")
    private Boolean isMinimumSoCChanged;

    /**
     * 最小SoC值
     */
    @JsonProperty("MinimumSoC")
    private Integer minimumSoC;

    /**
     * 功率因数是否已更改
     */
    @JsonProperty("IsPowerFactorChanged")
    private Boolean isPowerFactorChanged;

    /**
     * 功率因数值
     */
    @JsonProperty("PowerFactor")
    private Double powerFactor;
}
