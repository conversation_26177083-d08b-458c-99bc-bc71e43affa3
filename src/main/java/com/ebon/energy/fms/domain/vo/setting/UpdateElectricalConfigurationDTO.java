package com.ebon.energy.fms.domain.vo.setting;

import com.ebon.energy.fms.domain.vo.ElectricalConfiguration;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UpdateElectricalConfigurationDTO {

    @JsonProperty("SerialNumber")
    private String serialNumber;

    @JsonProperty("ElectricalConfiguration")
    private ElectricalConfiguration electricalConfiguration;

    @JsonProperty("IsFromDevice")
    private Boolean isFromDevice;

    // 兼容旧字段名
    @Deprecated
    private String sn;

    // 获取序列号的统一方法
    public String getSerialNumber() {
        return serialNumber != null ? serialNumber : sn;
    }
}
