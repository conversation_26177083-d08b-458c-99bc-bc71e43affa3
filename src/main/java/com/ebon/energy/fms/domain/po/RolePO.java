package com.ebon.energy.fms.domain.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RolePO {

    @NotBlank
    private String roleName;

    @NotNull
    private Boolean status;

    /**
     * 权限code列表
     */
    private List<String> permissionCodes;

    public boolean getStatus() {
        return this.status;
    }
}
