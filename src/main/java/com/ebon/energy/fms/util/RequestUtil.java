package com.ebon.energy.fms.util;

import com.ebon.energy.fms.common.enums.CommonErrorCodeEnum;
import com.ebon.energy.fms.common.exception.BizException;
import com.ebon.energy.fms.domain.vo.UserVO;
import com.google.common.base.Strings;
import com.google.common.net.InetAddresses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static com.ebon.energy.fms.common.constants.AuthorizeConstants.ATTRIBUTE_USER_KEY;

@Slf4j
public class RequestUtil {

    public static String getValueFromRequest(HttpServletRequest request, String paramName) {
        String paramValue = request.getParameter(paramName);
        if (Strings.isNullOrEmpty(paramValue)) {
            paramValue = CookieUtil.getValue(request, paramName);
            if (Strings.isNullOrEmpty(paramValue)) {
                paramValue = request.getHeader(paramName);
            }
        }
        return Strings.nullToEmpty(paramValue);
    }

    public static Integer getLoginUserId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        try {
            UserVO userVO = (UserVO) attributes.getAttribute(ATTRIBUTE_USER_KEY, 0);
            return userVO.getUserId();
        } catch (Exception e) {
            throw new BizException(CommonErrorCodeEnum.LOGIN_TOKEN_ERROR, "need login");
        }
    }

    public static String getPortolUserId(){
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        try {
            UserVO userVO = (UserVO) attributes.getAttribute(ATTRIBUTE_USER_KEY, 0);
            return userVO.getPortalUserId();
        } catch (Exception e) {
            throw new BizException(CommonErrorCodeEnum.LOGIN_TOKEN_ERROR, "need login");
        }
    }

    public static String getLoginUserEmail() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        try {
            UserVO userVO = (UserVO) attributes.getAttribute(ATTRIBUTE_USER_KEY, 0);
            return userVO.getEmail();
        } catch (Exception e) {
            throw new BizException(CommonErrorCodeEnum.LOGIN_TOKEN_ERROR, "need login");
        }
    }

    public static String getRemoteIp(HttpServletRequest request) {
        String ip;
        ip = request.getHeader("X-Real-IP");
        if (!Strings.isNullOrEmpty(ip) && InetAddresses.isInetAddress(ip)) {
            return ip;
        }
        ip = request.getHeader("X-Forwarded-For");
        if (!Strings.isNullOrEmpty(ip) && InetAddresses.isInetAddress(ip)) {
            return ip;
        }
        ip = request.getHeader("Proxy-Client-IP");
        if (!Strings.isNullOrEmpty(ip) && InetAddresses.isInetAddress(ip)) {
            return ip;
        }
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (!Strings.isNullOrEmpty(ip) && InetAddresses.isInetAddress(ip)) {
            return ip;
        }
        return request.getRemoteAddr();
    }

}
