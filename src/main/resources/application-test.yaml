spring:
  datasource:
    primary:
      jdbc-url: ************************************************************************************************************
      username: dev
      password: 3g%ytt8ir(adGF!
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    second:
      jdbc-url: *******************************************************************************************************************************
      username: dev
      password: 3g%ytt8ir(adGF!
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    third:
      jdbc-url: **********************************************************************************************************************
      username: dev
      password: 3g%ytt8ir(adGF!
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml

authorize:
  jwt:
    secret: "SP&v0P^jV3^b22Fg@DkY*8"
    expire-seconds: 259200

redback:
  baseUrl: https://rbtestwebapi-leia.azurewebsites.net
  environment: Test
  serviceAccountName: <EMAIL>
  serviceAccountPassword: R#dR@c!5150

tuya:
  accessKey: 8MZARua1s4KwqAsnVeGGsgA3
  secretKey: qkxpkpgJz8GJFIfG0FGci1uxaKFhCxJgy4dL8qo3g01PWtCl
  hostName: https://si-57124737fac545379b66.tuyacloud.com:8686

azure:
  iot:
    hub:
      connectionString: "HostName=rbtestiothub-leia.azure-devices.net;SharedAccessKeyName=iothubowner;SharedAccessKey=1Pg7hAYrKY9tCkl+k8+adylHxfp6c5MwWyr+7cbdsWs="
      hostName: "rbtestiothub-leia.azure-devices.net"
  storage:
    oldAccountName: rbtestleiastorage
    oldAccountKey: ****************************************************************************************
    telemetryAccountName: rbtesttelemetry
    telemetryAccountKey: ****************************************************************************************
    enableNewTelemetryStorage: false
    useOnlyNewTelemetryFromDate: "********" # 仅从2023年1月1日起使用新存储
    useOnlyOldStoragePriorToDate: "********" # 仅2022年12月31日之前使用旧存储


aliyun:
  tablestore:
    endPoint: https://redback-test.cn-hangzhou.ots.aliyuncs.com
    accessKey: LTAI5t8bo1AzeneVQgJkVsau
    secretKey: ******************************
    instanceName: redback-test
    telemetryStorageFromDateyyyyMMdd: ********
    telemetryOldStoragePriorToDateyyyyMMdd: ********

EMSStorageContainerURL: https://rbteststorageleia.blob.core.windows.net/emsfirmwares/

directMethodChangeHostnamePasswords: pmY7SeX18KOmaYztkiA-ZnBhYitCGktI,redbacksnewpassword

portal:
  url: https://rbtestwebui-leia.azurewebsites.net
  redbackBatterySwitch: true
  exportLimitMessageThresholdW: 200
  numberOfConsecutiveDaysBatteriesMismatchedToDisabled: 14

forecast:
  url: http://redback-forecast.redback.svc.ebonex-newtest:8000
  
# test.1234
fmsSystemDefaultPassword: 62a8c629801d5c4154fbaff1fd315b25